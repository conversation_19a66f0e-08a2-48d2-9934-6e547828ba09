<!-- 搜索区域 -->
<flc-search-container [headerTitle]="'costBreakdown.title' | translate" (reset)="onReset()">
  <!-- 下单日期 -->
  <div>
    <span class="search-name">{{ 'costBreakdown.list.search.orderDate' | translate }}：</span>
    <nz-range-picker
      [formControl]="searchForm.get('order_date_range')"
      [nzRanges]="dateRanges"
      [nzPlaceHolder]="['costBreakdown.list.search.startDate' | translate, 'costBreakdown.list.search.endDate' | translate]"
      nzFormat="yyyy-MM-dd"
      (ngModelChange)="onSearch()">
    </nz-range-picker>
  </div>

  <!-- 关键字 -->
  <!-- <div>
    <span class="search-name">关键字：</span>
    <input
      nz-input
      [formControl]="searchForm.get('keyword')"
      placeholder="请输入关键字"
      style="width: 160px;"
      (keyup.enter)="onSearch()">
  </div> -->

  <!-- 订单号 -->
  <div>
    <span class="search-name">{{ 'costBreakdown.list.search.orderNo' | translate }}：</span>
    <input
      nz-input
      [formControl]="searchForm.get('order_code')"
      [placeholder]="'costBreakdown.list.search.pleaseInput' | translate"
      style="width: 160px"
      (input)="onInputSearch()"
      (keyup.enter)="onSearch()" />
  </div>

  <!-- 交付单号 -->
  <div>
    <span class="search-name">{{ 'costBreakdown.list.search.deliveryNo' | translate }}：</span>
    <input
      nz-input
      [formControl]="searchForm.get('po_code')"
      [placeholder]="'costBreakdown.list.search.pleaseInput' | translate"
      style="width: 160px"
      (input)="onInputSearch()"
      (keyup.enter)="onSearch()" />
  </div>

  <!-- 款号 -->
  <div>
    <span class="search-name">{{ 'costBreakdown.list.search.styleNo' | translate }}：</span>
    <input
      nz-input
      [formControl]="searchForm.get('style_code')"
      [placeholder]="'costBreakdown.list.search.pleaseInput' | translate"
      style="width: 160px"
      (input)="onInputSearch()"
      (keyup.enter)="onSearch()" />
  </div>

  <!-- 品名 -->
  <div>
    <span class="search-name">{{ 'costBreakdown.list.search.productName' | translate }}：</span>
    <input
      nz-input
      [formControl]="searchForm.get('category')"
      [placeholder]="'costBreakdown.list.search.pleaseInput' | translate"
      style="width: 160px"
      (input)="onInputSearch()"
      (keyup.enter)="onSearch()" />
  </div>

  <!-- 客户 -->
  <div>
    <span class="search-name">{{ 'costBreakdown.list.search.customer' | translate }}：</span>
    <nz-select
      [formControl]="searchForm.get('customer')"
      [nzPlaceHolder]="'costBreakdown.list.search.pleaseSelect' | translate"
      nzAllowClear
      nzShowSearch
      style="width: 160px"
      (ngModelChange)="onSearch()">
      <nz-option *ngFor="let option of customerOptions" [nzValue]="option.value" [nzLabel]="option.label"> </nz-option>
    </nz-select>
  </div>
</flc-search-container>

<!-- 表格区域 -->
<div class="table-container" [class.has-detail]="showDetail">
  <div class="table-section">
    <!-- nz-table 表格 -->
    <nz-table
      #nzTable
      [nzData]="tableConfig.dataList"
      [(nzPageSize)]="tableConfig.pageSize"
      [nzLoading]="tableConfig.loading"
      [(nzPageIndex)]="tableConfig.pageIndex"
      [nzTotal]="tableConfig.count"
      [nzFrontPagination]="false"
      [nzShowPagination]="true"
      [nzShowSizeChanger]="true"
      [nzScroll]="{ x: '100%', y: this.tableConfig.height + 'px' }"
      (nzPageIndexChange)="onIndexChange($event)"
      (nzPageSizeChange)="onSizeChange($event)"
      nzBordered
      nzSize="middle">
      <!-- 表头 -->
      <thead>
        <tr>
          <th nzLeft nzWidth="36px">
            <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event, 1)">
              <i nz-icon nzType="setting" nzTheme="fill"></i>
            </a>
          </th>
          <ng-container *ngFor="let header of costTableHeader">
            <th
              [nzLeft]="header.disable || header.pinned"
              nz-resizable
              *ngIf="header.visible"
              [nzWidth]="header.width"
              [nzShowSort]="header.sort"
              [nzSortOrder]="header.key === 'order_date' ? orderDateSortOrder : null"
              [nzSortFn]="header.key === 'order_date' ? true : null"
              (nzSortOrderChange)="header.key === 'order_date' ? onOrderDateSortChange($event) : null"
              (nzResizeEnd)="onResize($event, item.label)">
              {{ header.label }}
            </th>
          </ng-container>
        </tr>
      </thead>

      <!-- 表体 -->
      <tbody>
        <tr
          *ngFor="let data of tableConfig.dataList; index as i"
          (click)="onViewDetail(data)"
          class="table-row-clickable"
          [class.selected]="selectedItem?.order_uuid === data.order_uuid">
          <td nzLeft="0px">{{ i + 1 }}</td>

          <ng-container *ngFor="let header of costTableHeader">
            <td [nzLeft]="header.disable || header.pinned" *ngIf="header.visible">
              <!-- 订单号链接 -->
              <ng-container *ngIf="header.key === 'order_code'">
                <a (click)="onGoToOrderDetail(data, $event)">{{ data.order_code }}</a>
              </ng-container>

              <!-- 交付单号显示 -->
              <ng-container *ngIf="header.key === 'po_codes'">
                <span *ngIf="data.po_codes && data.po_codes.length > 0; else noPoCodes">
                  {{ data.po_codes.join('、') }}
                </span>
                <ng-template #noPoCodes>-</ng-template>
              </ng-container>

              <!-- 交付日期显示 -->
              <ng-container *ngIf="header.key === 'po_due_dates'">
                <span *ngIf="data.po_due_dates && data.po_due_dates.length > 0; else noPoDates">
                  <span *ngFor="let timestamp of data.po_due_dates; let last = last">
                    {{ timestamp | date: 'yyyy-MM-dd' }}<span [style.visibility]="last ? 'hidden' : 'visible'">、</span>
                  </span>
                </span>
                <ng-template #noPoDates>-</ng-template>
              </ng-container>
              <!-- 下单日期显示 -->
              <ng-container *ngIf="header.key === 'order_date'">
                <flc-text-truncated [data]="data.order_date | date: 'yyyy-MM-dd'"></flc-text-truncated>
              </ng-container>

              <!-- 单价显示 -->
              <ng-container *ngIf="header.key === 'unit_prices'">
                <span *ngIf="data.unit_prices && data.unit_prices.length > 0; else noUnitPrices">
                  {{ data.unit_prices.join('、') }}
                </span>
                <ng-template #noUnitPrices>-</ng-template>
              </ng-container>

              <!-- 汇率显示 -->
              <ng-container *ngIf="header.key === 'express_rates'">
                <span *ngIf="data.express_rates && data.express_rates.length > 0; else noExpressRates">
                  {{ data.express_rates.join('、') }}
                </span>
                <ng-template #noExpressRates>-</ng-template>
              </ng-container>

              <!-- 普通字段显示 -->
              <ng-container *ngIf="!isSpecialField(header.key)">
                <flc-text-truncated [data]="data[header.key]"></flc-text-truncated>
              </ng-container>
            </td>
          </ng-container>
        </tr>
      </tbody>
    </nz-table>
  </div>

  <!-- 详情区域 -->
  <div class="detail-section" *ngIf="showDetail && selectedItem">
    <div class="detail-header">
      <h3>{{ selectedItem.order_code }} - {{ 'costBreakdown.list.detail.title' | translate }}</h3>
      <button nz-button nzType="text" (click)="onCloseDetail()">
        <i nz-icon nzType="close"></i>
      </button>
    </div>

    <!-- 详情标签页 -->
    <div class="detail-tabs-container">
      <!-- Tab标题和筛选区域 -->
      <div class="tabs-header">
        <nz-tabset
          [(nzSelectedIndex)]="activeTabIndex"
          (nzSelectedIndexChange)="onDetailTabChange($event)"
          nzType="card"
          class="detail-tabs">
          <!-- 物料信息标签页 -->
          <nz-tab [nzTitle]="materialTabTitle">
            <ng-template #materialTabTitle>
              <i nz-icon [nzType]="detailTabs[0].icon"></i>
              {{ 'costBreakdown.list.detail.materialInfo' | translate }}
            </ng-template>
          </nz-tab>

          <!-- 加工信息标签页 -->
          <nz-tab [nzTitle]="processingTabTitle">
            <ng-template #processingTabTitle>
              <i nz-icon [nzType]="detailTabs[1].icon"></i>
              {{ 'costBreakdown.list.detail.processingInfo' | translate }}
            </ng-template>
          </nz-tab>

          <!-- 收款信息标签页 -->
          <nz-tab [nzTitle]="paymentTabTitle">
            <ng-template #paymentTabTitle>
              <i nz-icon [nzType]="detailTabs[2].icon"></i>
              {{ 'costBreakdown.list.detail.paymentInfo' | translate }}
            </ng-template>
          </nz-tab>

          <!-- 费用分摊标签页 -->
          <nz-tab [nzTitle]="expenseTabTitle">
            <ng-template #expenseTabTitle>
              <i nz-icon [nzType]="detailTabs[3].icon"></i>
              {{ 'costBreakdown.list.detail.expenseAllocation' | translate }}
            </ng-template>
          </nz-tab>
        </nz-tabset>

        <!-- 物料信息筛选区域 - 只在物料信息tab时显示 -->
        <div class="material-filter-section" *ngIf="activeTabIndex === 0">
          <span class="filter-label">筛选：</span>
          <nz-checkbox-group [(ngModel)]="materialTypeOptions" (ngModelChange)="onMaterialFilterChange()"> </nz-checkbox-group>
        </div>
      </div>

      <!-- Tab内容区域 -->
      <div class="tabs-content">
        <!-- 物料信息内容 -->
        <div *ngIf="activeTabIndex === 0">
          <flss-material-info-tab [orderId]="selectedItem.order_uuid" [materialTypeFilter]="materialTypeFilter"> </flss-material-info-tab>
        </div>

        <!-- 加工信息内容 -->
        <div *ngIf="activeTabIndex === 1">
          <flss-processing-info-tab
            [orderId]="selectedItem.order_id"
            [bulkOrderCode]="selectedItem.order_code"
            [ioUuid]="selectedItem.order_uuid">
          </flss-processing-info-tab>
        </div>

        <!-- 收款信息内容 -->
        <div *ngIf="activeTabIndex === 2">
          <flss-payment-info-tab [orderId]="selectedItem.order_uuid"> </flss-payment-info-tab>
        </div>

        <!-- 费用分摊内容 -->
        <div *ngIf="activeTabIndex === 3">
          <flss-expense-allocation-tab [orderId]="selectedItem.order_uuid"> </flss-expense-allocation-tab>
        </div>
      </div>
    </div>
  </div>
</div>
