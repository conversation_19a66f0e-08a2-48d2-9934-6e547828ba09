/**
 * API请求参数
 */
export interface ReceiptDetailParams {
  account_id?: number;
  /**
   * 1 暂存，2提交
   */
  action_type?: number;
  code?: string;
  currency_id?: number;
  currency_name?: string;
  customer_id?: number;
  customer_name?: string;
  exchange_rate?: string;
  id?: number;
  /**
   * 应收款
   */
  payment_list?: ReceiptOrderSaveReqPayment[];
  /**
   * 预付款
   */
  pre_payment_list?: ReceiptOrderSaveReqPrePayment[];
  receipt_date?: number;
  remake?: string;
  type?: number;
  [property: string]: any;
}

/**
 * ReceiptOrderSaveReqPayment
 */
export interface ReceiptOrderSaveReqPayment {
  bills_receivable_id?: string;
  current_receipt_amount?: string;
  order_uuid?: string;
  outbound_id?: string;
  po_unique_code?: string;
  prepayment_occupied_amount?: string;
  [property: string]: any;
}

/**
 * ReceiptOrderSaveReqPrePayment
 */
export interface ReceiptOrderSaveReqPrePayment {
  current_receipt_amount?: string;
  order_uuid?: string;
  ratio?: string;
  [property: string]: any;
}

export enum AuditActionType {
  pass = '1',
  refuse = '2',
  receive = '3',
  cancel = '4',
}

/**
 * 审核请求参数
 */
export interface AuditParams {
  ids?: string[];
  action_type?: AuditActionType;
  back_reason?: string;
  cancel_reason?: string;
  version?: string;
}

/* 收款单详情相关接口定义 */

export interface ReceiptDetail {
  account_id?: string;
  back_reason?: string;
  /**
   * ：账号
   */
  bank_account?: string;
  /**
   * 开户银行
   */
  bank_name?: string;
  /**
   * code
   */
  code?: string;
  currency_id?: string;
  currency_name?: string;
  customer_id?: string;
  customer_name?: string;
  exchange_rate?: string;
  /**
   * id
   */
  id?: string;
  /**
   * 应收款详情
   */
  payment_list: ReceivableReceiptDetail[];
  /**
   * 预收款详情
   */
  pre_payment_list: AdvanceReceiptDetail[];
  /**
   * 应收日期
   */
  receipt_date?: number;
  remake?: string;
  /**
   * 类型
   */
  type?: number;
  [property: string]: any;
}

/* 应收款明细 - 扁平化结构用于表格展示 */
export interface ReceivableReceiptDetail {
  // 基础标识信息
  bills_receivable_code: string;
  bills_receivable_id?: string;
  outbound_code?: string;
  outbound_id?: string;
  order_code?: string;
  order_uuid: string;
  po_code?: string;
  po_unique_code?: string;

  // 业务字段
  style_code?: string; // 款号
  category?: string; // 品名
  delivery_codes?: string[]; // 交付单号列表
  quantity?: string; // 数量

  // 金额字段
  expected_amount?: string; // 应收金额
  received_amount?: string; // 已收金额
  pending_amount?: string; // 待收金额
  advance_amount?: string; // 预收款（可占用）
  used_advance_amount?: string; // 占用预收款
  current_receipt_amount?: string; // 本次收款
  current_receipt_amount_local?: string; // 本次收款（本币）
  actual_available_advance?: string; // 实际可占用预收款（考虑其他行占用）

  // 控制字段
  auto_use_advance?: boolean; // 自动占用预收款

  // 原始嵌套结构（用于数据处理）
  outbound_list?: V1BillsReceivableDetailOutbound[];
  [property: string]: any;
}

/**
 * v1BillsReceivableDetailOutbound
 */
export interface V1BillsReceivableDetailOutbound {
  order_list?: OutboundBulkOrder[];
  outbound_code?: string;
  outbound_id?: string;
  [property: string]: any;
}

/**
 * OutboundBulkOrder
 */
export interface OutboundBulkOrder {
  order_code?: string;
  order_uuid?: string;
  po_list?: OutboundBulkOrderPo[];
  [property: string]: any;
}

/**
 * OutboundBulkOrderPo
 */
export interface OutboundBulkOrderPo {
  category?: string;
  /**
   * 本次付款
   */
  current_receipt_amount?: string;
  /**
   * 本次付款本币
   */
  current_receipt_amount_local?: string;
  /**
   * 应收金额
   */
  expected_amount?: string;
  /**
   * 待收金额
   */
  pending_amount?: string;
  /**
   * 订单可占用金额
   */
  available_pre_payment?: string;
  po_code?: string;
  po_unique_code?: string;
  pre_payment?: string;
  qty?: string;
  /**
   * 已收金额
   */
  received_amount?: string;
  style_code?: string;
  [property: string]: any;
}

/* 预收款明细 */
export interface AdvanceReceiptDetail {
  /**
   * 品名
   */
  category?: string;
  /**
   * 币种名称
   */
  currency_name?: string;
  /**
   * 暂估金额
   */
  estimated_amount: string;
  /**
   * 订单code
   */
  order_code: string;
  /**
   * 订单数量
   */
  order_qty: string;
  /**
   * 订单uuid
   */
  order_uuid?: string;
  /**
   * 本次付款
   */
  payment: string | number;
  payment_local: string | number;
  /**
   * 交付单号
   */
  po_codes?: string[];
  /**
   * 预收款
   */
  pre_receiving_amount: string;
  pre_receiving_amount_local: string;
  /**
   * 已收款
   */
  received_amount: string;
  received_amount_local: string;
  /**
   * 款号
   */
  style_code?: string;
  [property: string]: any;
}
////////////////////////////////////////////////////////////////////////////////////

/* 预售单抽屉相关接口 */
export interface OrderSelectorItem {
  category?: string;
  /**
   * 创建人
   */
  create_name?: string;
  /**
   * 创建时间
   */
  create_time?: string;
  /**
   * 币种ID
   */
  currency_id?: string;
  /**
   * 币种
   */
  currency_type?: string;
  customer_name?: string;
  /**
   * 暂估金额
   */
  estimated_amount?: string;
  order_code: string;
  order_qty?: string;
  order_uuid?: string;
  po_codes?: string[];
  /**
   * 预付款
   */
  prepayment?: string;
  /**
   * 预付款本币
   */
  prepayment_local?: string;
  style_code?: string;
  [property: string]: any;
}

/* 应收单选择抽屉相关接口 */
export interface ReceivableSelectorItem {
  /**
   * 应收单code
   */
  bills_receivable_code: string;
  /**
   * 应收单ID
   */
  bills_receivable_id?: string;
  create_name?: string;
  create_time?: string;
  /**
   * 币种名称
   */
  currency_name?: string;
  /**
   * 应收金额
   */
  expected_amount?: string;
  /**
   * 关联发票
   */
  invoice_names?: string[];
  /**
   * 关联单据
   */
  order_codes?: string[];
  /**
   * 待收金额
   */
  pending_amount?: string;
  /**
   * 已收金额
   */
  received_amount?: string;
  received_date?: string;
  /**
   * 关联款号
   */
  style_codes?: string[];
  [property: string]: any;
}

/* 客户档案选项 */
export interface CustomerOption {
  /**
   * 客户编码
   */
  code?: string;
  id: string;
  /**
   * 客户全称
   */
  name?: string;
  /**
   * 客户简称
   */
  short_name: string;
  [property: string]: any;
}

/* 账户管理选项 */
export interface AccountOption {
  account_info?: string;
  id?: string;
  [property: string]: any;
}

/* 订单选择查询参数 */
export interface OrderSelectorParams {
  /**
   * 品名
   */
  category?: string;
  /**
   * 客户
   */
  customer?: string;
  /**
   * 结束时间
   */
  end_time?: number;
  /**
   * 创建人
   */
  gen_user_id?: number;
  /**
   * 订单号
   */
  order_code?: string;
  page?: number;
  /**
   * 交付单号
   */
  po_unique_code?: string;
  size?: number;
  /**
   * 开始时间
   */
  start_time?: number;
  /**
   * 款号
   */
  style_code?: string;
  [property: string]: any;
}

/* 应收单选择查询参数 */
export interface ReceivableSelectorParams {
  /**
   * 应收单号
   */
  bills_receivable_code?: string;
  /**
   * 关联发票
   */
  bills_receivable_id?: string[];
  /**
   * 品名
   */
  category?: string;
  /**
   * 结束时间
   */
  create_end_time?: number;
  /**
   * 开始时间
   */
  create_start_time?: number;
  /**
   * 创建人
   */
  created_by?: string;
  /**
   * 客户
   */
  customer_id?: string;
  /**
   * 关联单据
   */
  order_uuid?: string;
  page?: number;
  /**
   * 应收结束时间
   */
  expected_receipt_date_end?: number;
  /**
   * 应收开始时间
   */
  expected_receipt_date_start?: number;
  size?: number;
  /**
   * 款号
   */
  style_code?: string;
  [property: string]: any;
}

/* API响应数据 */
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

export interface ListResponse<T> {
  total: number;
  items: T[];
}

/* 表单配置 */
export interface FormConfig {
  label: string;
  key: string;
  type: 'input' | 'select' | 'date' | 'number' | 'textarea';
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  maxLength?: number;
  options?: { label: string; value: any }[];
  validator?: any[];
}

// 页面编辑状态
export enum PageEditStatusEnum {
  add = 'add',
  read = 'read',
  edit = 'edit',
}

export enum ReceiptTypeEnum {
  advance = 1,
  receivable = 2,
}

export enum OrderStatus {
  all = 0,
  toSubmit = 1, // 待提交
  toAudit = 2, // 待审核
  toModify = 3, // 待修改
  toPayee = 4, // 待收款
  payed = 5, // 已收款
  cancelled = 6, // 已取消
  toModifyAudit = 9, // 修改待审核
  modifyAuditReturn = 10, //修改审核未通过
  auditPass = 11, // 审核通过
}
