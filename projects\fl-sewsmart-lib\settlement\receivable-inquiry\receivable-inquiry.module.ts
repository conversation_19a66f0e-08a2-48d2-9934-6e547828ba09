import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReceivableInquiryListComponent } from './receivable-inquiry-list/receivable-inquiry-list.component';
import { FormsModule } from '@angular/forms';
import { ReceivableInquiryRoutingModule } from './receivable-inquiry-routing.module';
import { FlcComponentsModule, FlcDirectivesModule } from 'fl-common-lib';
import { FlButtonModule } from 'fl-ui-angular/button';
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { HttpClient } from '@angular/common/http';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzSpinModule } from 'ng-zorro-antd/spin';

const nzModules = [
  NzRadioModule,
  NzSelectModule,
  NzCheckboxModule,
  NzGridModule,
  NzTableModule,
  NzSpinModule
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ...nzModules,
    FlButtonModule,
    FlcDirectivesModule,
    FlcComponentsModule,
    ReceivableInquiryRoutingModule,
    TranslateModule.forChild({
          isolate: false,
          extend: true,
          loader: {
            provide: TranslateLoader,
            useFactory: (http: HttpClient) => {
              return new MultiTranslateHttpLoader(http, [
                { prefix: './assets/i18n/settlement/receivable-inquiry/', suffix: '.json' },
              ]);
            },
            deps: [HttpClient],
          },
        }),
  ],
  declarations: [ReceivableInquiryListComponent]
})
export class ReceivableInquiryModule { 
  constructor(public translateService: TranslateService) {
    const cl = translateService.currentLang;
    translateService.currentLang = '';
    translateService.use(cl);
  }
}
