import { Component, ElementRef, Inject, Input, OnInit, ViewChild } from '@angular/core';

import { OutStockService } from '../../outstock.service';
import { getTableHeaders, getSearchConfig } from './product-list.config';
import { FlcDrawerHelperService, FlcTableComponent } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { OutboundOrderTypeEnum } from '../../models/outstock.enum';

@Component({
  selector: 'app-product-list',
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss'],
})
export class ProductListComponent implements OnInit {
  env_project = 'sewsmart'; //默认系统环境变量
  translateSuffix = this._service.translateSuffix; // 翻译
  _translate = this._service.translateFn;
  constructor(
    private _service: OutStockService,
    private _flcDrawer: FlcDrawerHelperService,
    private _msg: NzMessageService,
    @Inject('environment') env: any
  ) {
    env.project && (this.env_project = env.project);
  }

  @Input() warehouse_id = 0;
  @Input() product_type = null;
  @Input() outbound_type = 0;
  @Input() disableList: any = [];
  @Input() outboundOrderType!: OutboundOrderTypeEnum; // 按订单or按款号

  outboundOrderTypeEnum = OutboundOrderTypeEnum;

  selectTotal = 0; // 选中的数量
  selectList = []; //选中的物料列表
  searchMatData = {}; //表格搜索数据

  matListSearch: any = []; // 表格搜索项
  matListHeader: any = []; //表格字段

  matTableConfig: any = {
    dataList: [],
    detailBtn: false,
    hasAction: true,
    hasCheckbox: true,
    pageIndex: 1,
    pageSize: 20,
    height: 400,
    count: 0,
    loading: false,
    nowRowDataId: null,
    actionWidth: '80px',
    uniqueId: 'uuid',
  };

  searchData: any = {};
  optionList: any = {};

  ngOnInit(): void {
    this.matTableConfig.tableName = `ProductListComponent+${this.outboundOrderType}`;
    this.matListSearch = getSearchConfig(this.outboundOrderType);
    this.matListHeader = getTableHeaders(this.outboundOrderType);
    this.getProductList();
    this.getSearchOption();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.calcTableHeight();
    }, 20);
  }

  getProductList(reset = false) {
    if (reset) {
      this.matTableConfig.pageIndex = 1;
    }
    const searchData = { ...this.searchData };

    const data = {
      warehouse_id: this.warehouse_id,
      outbound_type: this.outbound_type,
      ...searchData,
      page: this.matTableConfig.pageIndex,
      size: this.matTableConfig.pageSize,
      data_type: this.outboundOrderType,
    };
    this.matTableConfig.loading = true;

    this._service.getProductList(data).subscribe((res) => {
      this.matTableConfig.loading = false;
      // 维度：款号_颜色_尺码_仓库_货位
      const format_text = (v: any) => `${v.style_uuid}-${v.color.color_code}-${v.size.spec_code}-${v.location.value}`;
      // 订单维度 维度：订单_交付单_颜色_尺码_仓库_货位
      const format_text_order = (v: any) =>
        `${v.order_uuid || ''}-${v.po_uuid || ''}-${v.color.color_code || ''}-${v.size.spec_code || ''}-${v.location.value || ''}`;
      const format_fn = this.outboundOrderType === this.outboundOrderTypeEnum.Order ? format_text_order : format_text;
      res.data?.data_list.forEach((d: any) => {
        d.uuid = format_fn(d);
        const selected = this.disableList?.find((v: any) => d.uuid === format_fn(v));
        if (selected) {
          d.disabled = true;
        }
      });

      this.matTableConfig.count = res.data?.count || 200;
      this.matTableConfig = { ...this.matTableConfig, dataList: res.data?.data_list || [], count: res.data?.count };
    });
  }

  getSearchOption() {
    this._service.getProductOption(this.warehouse_id).subscribe((res: any) => {
      this.optionList = res.data || {};
    });
  }

  /**
   * 表格页码变化
   * @param e
   */
  indexChanges(e: number) {
    this.matTableConfig.pageIndex = e;
    this.getProductList();
  }
  /**
   * 表格pagesize变化
   * @param e
   */
  sizeChanges(e: number) {
    this.matTableConfig.pageSize = e;
    this.getProductList();
  }

  getCount(e: any) {
    this.selectTotal = e.count;
    this.selectList = e.list;
  }

  /**
   * 重置搜索
   */
  resetMatList() {
    this.searchData = {};
    this.getProductList(true);
  }

  onMatSearch() {
    this.getProductList(true);
  }

  emitSelect(item?: any) {
    const list = item ? [item] : this.selectList;
    let { product_type, warehouse_id } = list[0];
    this.product_type && (product_type = this.product_type);
    this.warehouse_id && (warehouse_id = this.warehouse_id);
    if (this.outboundOrderType === OutboundOrderTypeEnum.Style) {
      const invalidItem = list.find((v) => v.product_type !== product_type || v.warehouse_id !== warehouse_id);
      if (invalidItem) {
        this._msg.error('请选择同产品类型同仓库的款哦');
        return;
      }
    } else if (this.outboundOrderType === OutboundOrderTypeEnum.Order) {
      // 获取基准数据：如果已有禁用列表，使用禁用列表中的数据作为基准，否则使用当前选中的第一项
      let targetCustomer: string;
      let targetCurrencyId: number;
      let targetWarehouseId: number;

      if (this.disableList?.length) {
        targetCustomer = this.disableList[0].customer_name || this.disableList[0].customer;
        targetCurrencyId = this.disableList[0].currency_id;
        targetWarehouseId = this.disableList[0].warehouse_id;
      } else {
        targetCustomer = list[0].customer_name || list[0].customer;
        targetCurrencyId = list[0].currency_id;
        targetWarehouseId = list[0].warehouse_id;
      }

      // 校验所有选中项是否满足同客户、同币种、同仓库的条件
      const invalidItem = list.find(
        (v) => (v.customer_name || v.customer) !== targetCustomer || v.currency_id !== targetCurrencyId || v.warehouse_id !== targetWarehouseId
      );

      if (invalidItem) {
        this._msg.error('请选择同客户同币种同仓库的订单哦');
        return;
      }
    }

    this._flcDrawer.eventEmit.emit({ list: list, outbound_type: this.outbound_type, outboundOrderType: this.outboundOrderType });
    this._flcDrawer.closeAllDrawer();
  }

  @ViewChild('tableList') tableList!: FlcTableComponent;
  clearSelect() {
    this.tableList?.clearAllSelected();
  }

  /**
   * 获取弹窗高度
   */
  @ViewChild('searchBarWrap') searchBarWrap!: ElementRef<HTMLElement>;
  calcTableHeight() {
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const bodyHeight = document.querySelector('.ant-drawer-body')!.clientHeight;
    const searchBarHeight = this.searchBarWrap.nativeElement.clientHeight;
    const renderY = bodyHeight - searchBarHeight - 95;
    this.matTableConfig.height = `${renderY - 40}`;
    this.matTableConfig = { ...this.matTableConfig };
  }
}
