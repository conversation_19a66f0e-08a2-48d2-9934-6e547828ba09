import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ResData } from './models/accounts-payable-aging.interface';

@Injectable({
  providedIn: 'root'
})
export class AccountsPayableAgingService {

  constructor(private _http: HttpClient) { }

  // 获取应收明细列表选项
  getOptionList(supplier_name?: string | null) {
    return this._http?.get<ResData<any>>(`/service/procurement-inventory/accounts-receivable-age/v1/list/options?supplier_name=${supplier_name}`)
  }

  // 获取账龄明细列表
  getList(payload: any) {
    return this._http?.post<ResData<any>>('/service/procurement-inventory/accounts-receivable-age/v1/list', payload)
  }

  // 获取账龄汇总列表
  getSumList(payload: any) {
    return this._http?.post<ResData<any>>('/service/procurement-inventory/accounts-receivable-age/v1/statistics', payload)
  }


}
