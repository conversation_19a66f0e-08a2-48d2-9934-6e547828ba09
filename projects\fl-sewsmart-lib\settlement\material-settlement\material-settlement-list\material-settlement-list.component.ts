import { Component, OnInit, AfterViewInit, On<PERSON><PERSON>roy, ViewChild } from '@angular/core';
import { FlcOrderStatusCheckItem, FlcRouterEventBusService, resizable, FlcTableComponent, FlcExportModalComponent } from 'fl-common-lib';
import { Router } from '@angular/router';
import { initCheckData, initSearchData, initTableHeaders, StatusStyle } from '../material-settlement';
import {
  MaterialSettlementListParams,
  MaterialListItem,
  MaterialSettlementListData,
  SearchParams,
  SearchOptions,
} from '../models/material-settlement.interface';
import { endOfDay, format, startOfDay } from 'date-fns';
import { Subject, debounceTime, distinctUntilChanged, switchMap, finalize, Subscription } from 'rxjs';
import { MaterialSettlementService } from '../material-settlement.service';
import { TranslateService } from '@ngx-translate/core';
import { isNil } from 'lodash';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { InvoiceStatusEnum, InvoiceTypeEnum, MaterialSettlementStatusEnum } from '../models/material-settlement.enum';
import { RelativeInvoiceModalComponent } from '../components/relative-invoice-modal/relative-invoice-modal.component';
import { searchForms, exportConfig } from './material-settlement-list.config';

/**
 * string转成number
 * @param input
 * @returns
 */
const stringToNum = (input: any) => {
  if (isNil(input)) {
    return null;
  } else if (isNaN(Number(input))) {
    return 0;
  }
  return Number(input);
};

@Component({
  selector: 'flss-material-settlement-list',
  templateUrl: './material-settlement-list.component.html',
  styleUrls: ['./material-settlement-list.component.scss'],
})
@resizable()
export class MaterialSettlementListComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('tableRef') tableRef!: FlcTableComponent;
  @ViewChild('exportModal') exportModal!: FlcExportModalComponent;
  collapse = false;
  isExporting = false;
  bodyHeight!: number;
  searchData: MaterialSettlementListParams = initSearchData();
  checkData: Array<FlcOrderStatusCheckItem> = initCheckData();
  dataList = [];
  // 设置表头信息
  tableHeader = initTableHeaders();
  // 表格配置项
  tableConfig = {
    tableName: 'materialSettlement', // 一个页面多表格，且需保存表头信息时，需要设置表格名称
    // dataList: <MaterialListItem[]>[], //表格数据
    dataList: <MaterialSettlementListData[]>[], //表格数据
    hasCheckbox: true, //是否显示选中功能
    count: 100, //数据总数量
    pageIndex: 1, //当前页码
    pageSize: 20, //当前每页数量
    loading: false,
    settingBtnPos: 'start',
    height: 400,
    detailBtn: true,
  };
  routeListerner?: any;
  statusStyle = StatusStyle;
  private searchChange$ = new Subject<MaterialSettlementListParams>();
  selectedId!: number;
  _btnArr!: string[];
  selectedIds = [];
  selectedList: MaterialSettlementListData[] = [];
  // statusOptions = initStatusOptions();
  searchList = searchForms;

  searchParams: SearchParams = {};
  searchOptions: SearchOptions = {};

  supplierOptions: {
    active: boolean;
    label: string;
    source_type: string;
    value: string;
    disabled?: boolean;
  }[] = [];
  partyArchiveOptions: {
    label: string;
    value: string;
  }[] = [];
  // invoiceStatusEnum = InvoiceStatusEnum;

  invoiceTypeEnum = InvoiceTypeEnum;
  actionMap = {
    hasCreate: false,
    hasAudit: false,
  };

  private _refreshSubscription?: Subscription;

  constructor(
    private _router: Router,
    private _routerEventBus: FlcRouterEventBusService,
    private _service: MaterialSettlementService,
    private _translateService: TranslateService,
    private _notice: NzNotificationService,
    private _msg: NzMessageService,
    private _modalService: NzModalService
  ) {}

  ngOnInit(): void {
    console.log('tableHeader', this.tableHeader);
    this._refreshSubscription = this._service.refreshEvent.subscribe(() => {
      this.refresh();
    });
    this.initUserActions();
    this.actionMap = this._service.getUserActions();
    this.tableConfig.detailBtn = this._btnArr.includes('settlement:material-settlement-detail');
    this.tableConfig = { ...this.tableConfig };
    (this as any).addResizePageListener();
    this.addRouterListener();
    this.checkData?.forEach((item) => (item.label = this._translateService.instant('materialSettlementStatus.' + item?.label)));
    this.getOption();
    this.getPartyArchive();
    this.initTabel();
    this.onSearch(true);
  }

  /**
   * 权限配置
   * @returns void
   */
  private initUserActions(): void {
    this._btnArr = [];
    const _actionList = this._service.getUserActionsMap()?.get('settlement/material-settlement');
    if (!_actionList) return;
    this._btnArr = _actionList;
  }

  addRouterListener() {
    this.routeListerner = this._routerEventBus.onRouterActivationEnd('/settlement/material-settlement/list', () => {
      setTimeout(() => {
        if (this._service.isChange) {
          const id = this.selectedId;
          this.getOption();
          this.getPartyArchive();
          this.onSearch();
          this.selectedId = id;
        }
      }, 200);
    });
  }

  removeRouterListener() {
    this.routeListerner?.unsubscribe();
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
    this.removeRouterListener();
    this.searchChange$?.unsubscribe();
    this._refreshSubscription?.unsubscribe();
  }

  /**
   * 计算页面高度
   */
  resizePage(): void {
    setTimeout(() => {
      const searchClientRect = document.querySelector('.flc-search-container')?.getBoundingClientRect() as any;
      const searchHeight = searchClientRect?.height > 0 ? searchClientRect?.height + 8 : 0;
      this.bodyHeight = window.innerHeight - searchHeight - 48 - 16;
      const bodyTitleClientRect = document.querySelector('.app-title-bar')?.getBoundingClientRect() as any;
      const bodyTitleHeight = bodyTitleClientRect?.height > 0 ? bodyTitleClientRect?.height + 8 : 0;
      //content最小高度
      if (this.bodyHeight < 200) {
        this.bodyHeight = 200;
      }
      this.tableConfig.height = this.bodyHeight - 18 - bodyTitleHeight - 32 - 30;
      this.tableConfig = { ...this.tableConfig };
    });
  }

  /**
   * 获取供应商下拉数据（来自供应商档案，启用禁用均有）
   */
  getOption() {
    this._service.getSupplierOption()?.subscribe((res) => {
      if (res?.code === 200) {
        // this.supplierOptions = res?.data?.option_list ?? [];
        this.searchOptions.supplier_id = res?.data?.option_list ?? [];
      }
    });
  }

  /**
   * 初始化table接口
   */
  initTabel() {
    this.searchChange$
      .pipe(
        debounceTime(500),
        distinctUntilChanged(),
        switchMap((obj) => this._service.getMaterialSettlementList({ ...obj }))
      )
      .subscribe(
        (res) => {
          if (res?.code === 200) {
            this.tableConfig.dataList = (res?.data?.list ?? []).map((item: any) => {
              return {
                ...item,
                tax_rate: item.invoice_list?.map((item: any) => item.tax_rate),
                // 是否可点击
                disabled: item.status === MaterialSettlementStatusEnum.cancelled,
              };
            });
            this.tableConfig.count = res?.data?.count ?? 0;
            this.tableConfig.loading = false;
            this.tableConfig = { ...this.tableConfig };

            const data = res?.data?.count_detail ?? {};
            let count = 0;
            this.checkData?.forEach((item: any) => {
              item.amount = data[item?.value] ?? 0;
              count = count + item.amount;
            });
            this.checkData[0].amount = count;
          } else {
            this.tableConfig.loading = false;
            this.tableConfig = { ...this.tableConfig };
          }

          // 同步更新选中的数据，确保选中数据是最新的
          this.syncSelectedListWithLatestData();
        },
        (error) => {
          console.log('list_Err', error);
          this.tableConfig.loading = false;
          this.tableConfig = { ...this.tableConfig };
        }
      );
  }

  onHandleWhere() {
    this.searchList
      .filter((item) => item.type === 'time')
      .forEach((item) => {
        const timeKeys = item.paramsKeys || [];
        const _values = this.searchData[item.valueKey] as number[];
        if (this.searchData[item.valueKey] && _values.length) {
          const startTime = Number(format(startOfDay(_values[0]), 'T'));
          const endTime = Number(format(endOfDay(_values[1]), 'T'));
          this.searchData[timeKeys[0]] = startTime;
          this.searchData[timeKeys[1]] = endTime;
        } else {
          this.searchData[timeKeys[0]] = null;
          this.searchData[timeKeys[1]] = null;
        }
      });
    this.onSearch();
  }

  hanlePayload() {
    const payload = {
      ...this.searchData,
      procurement_id: stringToNum(this.searchData.procurement_id),
      supplier_id: stringToNum(this.searchData.supplier_id),
      credit_days: stringToNum(this.searchData.credit_days),
      gen_user_id: stringToNum(this.searchData.gen_user_id),
    };
    delete payload.confirm_time_date;
    delete payload.gen_time_date;
    delete payload.order_finish_date;
    delete payload.payable_time_date;
    return payload;
  }
  /**
   * 触发搜索
   */
  onSearch(reset = false) {
    this.searchData.page = reset ? 1 : this.searchData.page;
    this.tableConfig.pageIndex = this.searchData.page || 1;
    this.tableConfig.loading = true;
    this.tableConfig = { ...this.tableConfig };
    const payload = this.hanlePayload();
    this.searchChange$.next(payload);
    this.selectedId = 0;
  }

  /**
   * 重置
   * 清空搜索项
   * 重置状态筛选
   * 重置页码
   * 重置排序
   */
  reset(): void {
    this.searchData = {
      ...initSearchData(),
      size: this.searchData.size,
    };
    this.checkData?.forEach((item) => {
      if (item?.isTotalItem) {
        item.checked = true;
      } else {
        item.checked = false;
      }
    });
    this.tableHeader.forEach((item: any) => {
      item.sortOrderBy = null;
    });
    this.onSearch(true);
    this.tableConfig.pageIndex = this.searchData.page || 1;
  }

  /**
   * 搜索项的展开收起
   * 触发页面计算
   */
  expandChange(): void {
    this.collapse = !this.collapse;
    this.resizePage();
  }

  /**
   * 状态筛选变更
   * @param $event
   */
  onCheckChange($event: any) {
    this.searchData.status = $event;
    this.onSearch(true);
  }

  /**
   * 新建
   */
  createMaterialSettlement(): void {
    this.selectedId = 0;
    const url = this._router.routerState.snapshot.url;
    this._router.navigate([url, 'new']);
  }

  /**
   * 页码变化
   * @param e
   */
  indexChanges(e: number) {
    this.tableConfig.pageIndex = e;
    this.refreshConfig();
  }

  /**
   * 每页数量变化
   * @param e
   */
  sizeChanges(e: number) {
    this.tableConfig.pageSize = e;
    this.tableConfig.pageIndex = 1;
    this.refreshConfig();
  }

  /**
   * 重新获取数据
   */
  refreshConfig() {
    this.searchData.page = this.tableConfig.pageIndex;
    this.searchData.size = this.tableConfig.pageSize;
    this.onSearch();
  }

  /**
   * 排序
   * @param param0
   */
  sortDataLists({ value, key }: { value: 'desc' | 'asc' | null; key: string }) {
    const orderType = `${value}` !== 'null' ? (`${value}` !== 'desc' ? 1 : 2) : 0;
    if (`${key}` === 'gen_time') {
      this.searchData.gen_time_order = orderType;
    }
    this.onSearch();
  }

  /**
   * 进入详情页
   * @param data
   */
  goDetail(data: any) {
    this.selectedId = data.id;
    const url = this._router.routerState.snapshot.url;
    this._router.navigate([url, data.id]);
  }
  // 选中的数量
  onSelectedCount(e: any) {
    // this.selectedIds = e.list.map((item: any) => item.id);
    // this.selectedList = e.list;

    // 确保选中的数据是最新的，从当前数据列表中获取最新状态
    const latestDataList = this.tableConfig.dataList;
    const updatedSelectList: MaterialSettlementListData[] = [];

    e.list.forEach((selectedItem: any) => {
      const latestItem = latestDataList.find((item) => item.id === selectedItem.id);
      if (latestItem) {
        updatedSelectList.push(latestItem);
      } else {
        // 如果在最新数据中找不到，使用原数据（可能是新数据还未加载完成）
        updatedSelectList.push(selectedItem);
      }
    });

    this.selectedList = updatedSelectList;
  }
  // 导出
  showExportTableModal() {
    const exportModalData = {
      version: '1.1',
      tablename: 'export',
      customConfig: JSON.parse(JSON.stringify(exportConfig)),
    };
    this.exportModal.showModel(exportModalData);
  }
  onExport() {
    if (this.isExporting) return;
    const column = this.exportModal.getCheckedList();
    this.isExporting = true;
    const params = {
      ...this.hanlePayload(),
      page: 1,
      size: 9999,
      ids: this.selectedIds,
      column,
    };
    this._service
      .export(params)
      .pipe(
        finalize(() => {
          this.isExporting = false;
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          const url: any = res?.data?.url;
          const name = '物料结算单' + '_' + format(new Date(), 'yyyyMMdd');
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', name + '.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this._notice.success('导出成功', '');
          this.resetTableSelect();
        } else {
          this._notice.error('导出失败', '');
        }
      });
  }
  resetTableSelect() {
    this.tableRef.clearAllSelected();
  }

  keywords: string | null = '';
  onInputSearchValue(value: string) {
    this.keywords = value.length ? value : null;
    this.tableConfig.pageIndex = 1;
    this.searchData.code_like = this.keywords;
    this.onSearch();
  }

  refresh() {
    this.getOption();
    this.getPartyArchive();
    this.initTabel();
    this.onSearch(true);
  }
  // 结算主体
  getPartyArchive() {
    this._service.getOptionsByColumn('PartyArchive').subscribe((res: any) => {
      if (res.code === 200) {
        this.partyArchiveOptions = res?.data?.option_list.map((item: any) => {
          return {
            label: item.name,
            value: item.id,
          };
        });
      }
    });
  }

  /**
   * 同步更新选中的数据，确保选中数据是最新的
   * 当数据刷新后，需要将selectList中的数据更新为最新的数据
   */
  private syncSelectedListWithLatestData() {
    if (this.selectedList.length === 0) {
      return;
    }

    const latestDataList = this.tableConfig.dataList;
    const updatedSelectList: MaterialSettlementListData[] = [];

    // 遍历当前选中的数据，从最新数据中找到对应的项并更新
    this.selectedList.forEach((selectedItem) => {
      const latestItem = latestDataList.find((item) => item.id === selectedItem.id);
      if (latestItem) {
        updatedSelectList.push(latestItem);
      } else {
        // 如果在最新数据中找不到，保留原数据（可能是分页或筛选导致的）
        updatedSelectList.push(selectedItem);
      }
    });

    // 更新选中的数据列表
    this.selectedList = updatedSelectList;
  }

  private isEmpty(): boolean {
    if (!this.selectedList.length) {
      this._notice.error('请至少选择一条数据', '');
      return true;
    }
    return false;
  }

  private clearAllSelected() {
    this.tableRef.clearAllSelected();
    this.tableConfig = { ...this.tableConfig };
  }

  // 批量关联账单
  onRelatedInovice() {
    if (this.isEmpty()) return;
    const _invalidCodes: string[] = [];
    const validItems: MaterialSettlementListData[] = [];
    this.selectedList.forEach((item) => {
      item.status == MaterialSettlementStatusEnum.toBeWrittenOff ? validItems.push(item) : _invalidCodes.push(item.code);
    });
    if (_invalidCodes.length) {
      this._msg.warning(`结算单号${_invalidCodes.join('、')}不能操作关联发票`, { nzDuration: 10000 });
    }
    if (!validItems.length) return;

    const _item = validItems[0];
    if (
      !validItems.every((item) => item.supplier_id === _item.supplier_id && item.party_archive_full_name === _item.party_archive_full_name)
    ) {
      this._msg.error('非同一供应商，且同一结算主体，无法同时批量关联发票！');
      return;
    }
    const _modal = this._modalService.create({
      nzTitle: '关联发票',
      nzFooter: null,
      nzWidth: '1100px',
      nzMaskClosable: false,
      nzKeyboard: false,
      nzComponentParams: {
        datalist: validItems,
      },
      nzBodyStyle: { padding: '0px' },
      nzContent: RelativeInvoiceModalComponent,
      nzOnOk: (comp) => {
        const _value = comp.handlePayload();
        this._service.batchInvoice(_value).subscribe((res) => {
          if (res.code === 200) {
            _modal.close();
            this.clearAllSelected();
            this._msg.success('关联成功');
            this.refresh();
          }
        });
      },
    });
  }
}
