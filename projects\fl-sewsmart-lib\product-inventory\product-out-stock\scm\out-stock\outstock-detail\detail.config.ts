import { FlcTableHeaderConfig } from 'fl-common-lib';
import { OutboundInfo, FormConfig, OutboundSaveLine, StockListOption } from '../models/outstock.interface';

export const formConfig: FormConfig<OutboundInfo, StockListOption>[] = [
  {
    label: '出库单号',
    code: 'code',
    required: true,
    span: 6,
    labelSpan: 6,
    type: 'input',
    maxLength: 20,
  },
  {
    label: '出库类型',
    code: 'outbound_type',
    optionKey: 'outbound_types',
    required: true,
    span: 6,
    labelSpan: 6,
    type: 'select',
  },
  {
    label: '产品类型',
    code: 'product_type_name',
    required: false,
    span: 6,
    labelSpan: 6,
    type: 'input',
    disable: true,
    placeholder: '自动带出',
  },
  {
    label: '客户',
    code: 'customer_name',
    required: false,
    span: 6,
    labelSpan: 6,
    type: 'input',
    disable: true,
    placeholder: '自动带出',
    hide: true, // 默认隐藏，只有出库类型为5时才显示
  },
  {
    label: '出库仓库',
    code: 'warehouse_id',
    optionKey: 'warehouse_ids',
    required: true,
    span: 6,
    labelSpan: 6,
    type: 'select',
  },
  {
    label: '备注',
    code: 'remark',
    required: false,
    span: 12,
    labelSpan: 4,
    maxLength: 255,
    type: 'textarea',
  },
];

export const formReadConfig: Partial<FormConfig<OutboundInfo, StockListOption>>[] = [
  {
    label: '出库单号',
    code: 'code',
    required: true,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '出库类型',
    code: 'outbound_type_name',
    required: true,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '产品类型',
    code: 'product_type_name',
    required: false,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '客户',
    code: 'customer_name',
    required: false,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '出库仓库',
    code: 'warehouse_name',
    required: true,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '出库状态',
    code: 'outbound_status_name',
    required: false,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '出库时间',
    code: 'outbound_time',
    required: false,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '创建时间',
    code: 'gen_time',
    required: false,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '创建人',
    code: 'gen_user_name',
    required: false,
    span: 6,
    labelSpan: 6,
  },
  {
    label: '备注',
    code: 'remark',
    required: false,
    span: 12,
    labelSpan: 4,
  },
];

export type SaveLineTh = FlcTableHeaderConfig<OutboundSaveLine> & { required?: true };
export const renderHeader: SaveLineTh[] = [
  {
    label: '款式编码',
    key: 'style_code',
    pinned: true,
    width: '150px',
    visible: true,
    type: 'text',
    disable: false,
    resizeble: true,
  },
  {
    label: '品名',
    key: 'category',
    pinned: false,
    width: '150px',
    visible: true,
    type: 'text',
    disable: false,
    resizeble: true,
  },
  {
    label: '供应商',
    key: 'supplier_name',
    width: '100px',
    visible: true,
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '供应商货号',
    key: 'supplier_art_num',
    width: '100px',
    visible: true,
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '颜色',
    key: 'color',
    width: '100px',
    visible: true,
    type: 'template',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '尺码',
    key: 'size',
    width: '100px',
    visible: true,
    type: 'template',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '货位',
    key: 'location',
    width: '160px',
    visible: true,
    type: 'template',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '库存数量',
    type: 'text',
    key: 'inventory_qty',
    width: '100px',
    visible: true,
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '出库数量',
    type: 'template',
    key: 'outbound_qty',
    width: '120px',
    visible: true,
    pinned: false,
    disable: false,
    resizeble: true,
    required: true,
  },
  {
    label: '箱数',
    type: 'template',
    key: 'box_qty',
    width: '120px',
    visible: true,
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '箱号',
    type: 'template',
    key: 'box_number',
    width: '120px',
    visible: true,
    pinned: false,
    disable: false,
    resizeble: true,
  },
];
