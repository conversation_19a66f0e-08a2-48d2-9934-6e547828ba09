import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { FlcSpKeyConstant, FlcSpUtilService } from 'fl-common-lib';
import { Observable } from 'rxjs';
import { UserActions } from './models/outstock.interface';

const baseApi = '/service/procurement-inventory/product-outbound/v1/web/elan/';

@Injectable()
export class OutStockService {
  isChange = false; // 是否有改变
  env_project = 'sewsmart'; //默认系统环境变量
  constructor(
    private http: HttpClient,
    private router: Router,
    private _spUtil: FlcSpUtilService,
    @Inject('environment') env: any,
    private _translate: TranslateService
  ) {
    env.project && (this.env_project = env.project);
  }

  /******************* 翻译相关 *******************/
  translateSuffix = 'product-inventory.product-out-stock.scm.';
  translateFn = (key: string, suffix?: string, interpolateParams?: object) => {
    return this._translate.instant((suffix ? suffix : this.translateSuffix) + key, interpolateParams);
  };

  /******************* 权限相关 *******************/

  userActions!: Array<string>;
  userAuthBtn: UserActions = { create: false };
  getUserActions() {
    // elan
    if (!this.userActions && this._spUtil.containsObjectKey(FlcSpKeyConstant.UserActions)) {
      const uaKey = 'product-inventory/product-out-stock/scm';
      const actionPrefix = 'inventory:product-out-stock-scm-';
      const actionMap = this._spUtil.getObject(FlcSpKeyConstant.UserActions) as Map<string, []>;
      this.userActions = actionMap.get(uaKey) as string[];
      this.userAuthBtn = {
        create: this.userActions.includes(actionPrefix + 'create'),
      };
    }
    return this.userAuthBtn;
  }
  /*********************************************************************************/

  // 新建出库单 下拉搜索数据
  getSelectOption() {
    return this.http.get<any>(baseApi + 'create-option');
  }

  /**
   * 获取出库单code
   * @param
   */
  getOutboundCode() {
    return this.http.get<any>(baseApi + 'create-code');
  }

  /**
   * 提交出库单
   * @param
   */
  saveBound(payload: any) {
    return this.http.post<any>(baseApi + 'save', payload);
  }

  /**
   * 获取出库单详情
   * @param
   */
  getOutboundDetail(id: any) {
    return this.http.get<any>(baseApi + 'detail', { params: { id } });
  }

  /**
   * 出库单删除
   * @param  {any} payload
   */
  deleteStock(data: any): Observable<any> {
    return this.http.post<any>(baseApi + 'delete', data);
  }

  /**
   * 出库单页面下拉框
   * @param
   */
  getOutboundOption() {
    return this.http.get<any>(baseApi + 'option');
  }

  /**
   * 出库单列表
   * @param
   */
  getList(data: any) {
    return this.http.post<any>(baseApi + 'list', data);
  }

  /**
   * 出库单列表树数量
   **/
  getListCount(data: any) {
    return this.http.post<any>(baseApi + 'list/count', data);
  }

  /**
   * 成品列表
   * @param
   */
  getProductList(data: any) {
    return this.http.post<any>(baseApi + 'addon-list', data);
  }

  /**
   * 成品列表下拉
   */
  getProductOption(warehouse_id?: number) {
    return this.http.post<any>(baseApi + 'addon-option', { warehouse_id: warehouse_id });
  }
  /**
   * 导出
   */
  export(params: any) {
    const url = '/service/procurement-inventory/product-outbound/v1/web/elan/list/export';
    return this.http.post(url, params);
  }

  /**
   * 核验客户支付额度
   * @param params
   */
  validPaymentQuota(params: { customer_id: string; currency_id: string }) {
    return this.http.post<any>('/service/procurement-inventory/archive/v1/customer/valid-payment-quota', params);
  }
}
