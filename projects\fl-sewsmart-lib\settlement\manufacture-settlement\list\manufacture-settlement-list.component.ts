import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';
import { FlcModalService, FlcTableComponent, resizable, FlcExportModalComponent } from 'fl-common-lib';
import { Subscription, finalize } from 'rxjs';
import { endOfDay, format, startOfDay } from 'date-fns';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ActivatedRoute, Router } from '@angular/router';
import { ManufactureListItem, SearchOptions, SearchParams } from '../model/manufacture-settlement.interface';
import { initStatusOptions, initTableHeader, searchForms, exportConfig } from './manufacture-settlement-list.config';
import { InvoiceStatusEnum, InvoiceTypeEnum, ManufactureOrderStatusEnum } from '../model/manufacture-settlement.enum';
import { ManufactureSettlementService } from '../manufacture-settlement.service';
import { RelatedInoviceModalComponent } from '../components/related-inovice-modal/related-inovice-modal.component';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { SettlementReturnModalComponent } from '../components/settlement-return-modal/settlement-return-modal.component';

@Component({
  selector: 'app-manufacture-settlement-list',
  templateUrl: './manufacture-settlement-list.component.html',
  styleUrls: ['./manufacture-settlement-list.component.scss'],
})
@resizable()
export class ManufactureSettlementListComponent implements OnInit {
  @ViewChild('tableBox') tableBoxRef?: ElementRef<HTMLDivElement>;
  @ViewChild(FlcTableComponent) flcTable!: FlcTableComponent;
  @ViewChild('exportModal') exportModal!: FlcExportModalComponent;

  constructor(
    private _router: Router,
    private _activeRoute: ActivatedRoute,
    private _modalService: NzModalService,
    private _service: ManufactureSettlementService,
    private _msg: NzMessageService,
    private _notice: NzNotificationService,
    private _flcModalService: FlcModalService
  ) {}

  searchList = searchForms;
  isExporting = false;
  searchParams: SearchParams = {};
  statusOptions = initStatusOptions();
  tableHeader = initTableHeader();
  searchOptions: SearchOptions = {};
  invoiceStatusEnum = InvoiceStatusEnum;
  invoiceTypeEnum = InvoiceTypeEnum;
  statusEnum = ManufactureOrderStatusEnum;
  actionMap = {
    hasCreate: false,
    hasAudit: false,
  };

  private _refreshSubscription?: Subscription;

  flcTableConfig = {
    dataList: <ManufactureListItem[]>[],
    count: 0,
    height: 500,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    translateName: '',
    detailBtn: true,
    canSetHeader: true,
    actionWidth: '105px',
    hasCheckbox: true,
    version: '1.0.2',
    settingBtnPos: 'start',
    trCheck: true,
    inactiveId: 'status',
    inactiveValue: ManufactureOrderStatusEnum.canceled,
  };

  ngOnInit() {
    this.refresh();
    this._refreshSubscription = this._service.refreshEvent.subscribe(() => {
      this.refresh();
    });
    (this as any).addResizePageListener();
    this.actionMap = this._service.getUserActionsMap();
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  ngOnDestroy() {
    (this as any).removeResizePageListener();
    this._refreshSubscription?.unsubscribe();
  }

  resizePage() {
    setTimeout(() => {
      const _boxElTop = this.tableBoxRef?.nativeElement.getBoundingClientRect().top || 0;
      const height = window.innerHeight - _boxElTop - 110;
      this.flcTableConfig = { ...this.flcTableConfig, height: height };
    });
  }

  // 获取列表
  private getList() {
    this.flcTableConfig = {
      ...this.flcTableConfig,
      loading: true,
    };
    const params = {
      ...this.searchParams,
      size: this.flcTableConfig.pageSize,
      page: this.flcTableConfig.pageIndex,
    };
    this._service.getList(params).subscribe((res) => {
      if (res.code === 200) {
        this.flcTableConfig.count = res.data.total;
        this.getStatusCount(res.data.status || [], res.data.invoice_status || []);
        this.flcTableConfig.dataList = res.data.list.map((item: any) => {
          return {
            ...item,
            tax_rate: item.invoice_list?.map((item: any) => item.tax_rate),
            disabled: item.status === ManufactureOrderStatusEnum.canceled,
          };
        });

        // 同步更新选中的数据，确保选中数据是最新的
        this.syncSelectedListWithLatestData();

        this.flcTableConfig.loading = false;
        this.flcTableConfig = { ...this.flcTableConfig };
      }
    });
  }

  // 获取下拉选项
  private getSearchOptions() {
    this._service.getSearchOptions().subscribe((res) => {
      this.searchOptions = res.data;
    });
  }

  // 获取状态数量
  private getStatusCount(statusOption: any[], invoiceStatusOptions: any[]) {
    let _total = 0;
    this.statusOptions.map((d: any) => {
      const _options = d.type !== 'invoice_status' ? statusOption : invoiceStatusOptions;
      const _amount = _options.find((op) => op.status == d.value);
      d.amount = _amount?.count || 0;
      if (d.type === 'invoice_status') return;
      _total += d.amount || 0;
    });
    this.statusOptions[0].amount = _total;
  }

  onCreate() {
    this._router.navigate(['.', 'add'], { relativeTo: this._activeRoute });
  }

  onSearch(searchParams: SearchParams) {
    this.searchParams = {
      ...this.searchParams,
      ...searchParams,
    };
    this.getList();
  }

  // 批量关联账单
  onRelatedInovice() {
    if (this.isEmpty()) return;
    const _invalidCodes: string[] = [];
    const validItems: ManufactureListItem[] = [];
    this.selectList.forEach((item) => {
      item.status !== ManufactureOrderStatusEnum.completed || item.invoice_status !== InvoiceStatusEnum['未核销']
        ? _invalidCodes.push(item.code)
        : validItems.push(item);
    });
    if (_invalidCodes.length) {
      this._msg.warning(`结算单号${_invalidCodes.join('、')}不能操作关联发票`, { nzDuration: 10000 });
    }
    if (!validItems.length) return;

    const _item = validItems[0];
    if (!validItems.every((item) => item.factory_id === _item.factory_id && item.party_archive_id === _item.party_archive_id)) {
      this._msg.error('非同一加工厂，且同一结算主体，无法同时批量关联发票！');
      return;
    }
    const _modal = this._modalService.create({
      nzTitle: '关联发票',
      nzFooter: null,
      nzWidth: '1100px',
      nzMaskClosable: false,
      nzKeyboard: false,
      nzComponentParams: {
        datalist: validItems,
      },
      nzBodyStyle: { padding: '0px' },
      nzContent: RelatedInoviceModalComponent,
      nzOnOk: (comp) => {
        const _value = comp.handlePayload();
        this._service.batchInvoice(_value).subscribe((res) => {
          if (res.code === 200) {
            _modal.close();
            this.clearAllSelected();
            this._msg.success('关联成功');
            this.refresh();
          }
        });
      },
    });
  }

  /***** 列表操作按钮 *****/

  detailId = 0;
  onDetail(item: ManufactureListItem) {
    this.detailId = item.id;
    this._router.navigate(['.', item.id], { relativeTo: this._activeRoute });
  }

  // 状态筛选
  onCheckChange(checks: Array<number>) {
    if (!checks.length) {
      this.searchParams = {
        ...this.searchParams,
        status: null,
        invoice_status: null,
      };
    } else {
      const obj =
        typeof checks[0] === 'string' ? { invoice_status: Number(checks[0]), status: null } : { status: checks[0], invoice_status: null };
      this.searchParams = {
        ...this.searchParams,
        ...obj,
      };
    }
    this.getList();
  }

  sizeChanges(size: number) {
    this.flcTableConfig = { ...this.flcTableConfig, pageSize: size };
    this.onSearch(this.searchParams);
  }

  indexChanges(page: number) {
    this.flcTableConfig = { ...this.flcTableConfig, pageIndex: page };
    this.getList();
  }

  /***** 排序 *****/
  sortOrderChange({ value }: { value: 'desc' | 'asc' | null }) {
    // this.searchData.sort_gen_time = value === 'asc' ? 2 : 1;
    // this.getList();
  }

  selectList: ManufactureListItem[] = [];
  onSelectedLine(data: { count: number; list: Array<ManufactureListItem> }) {
    // 确保选中的数据是最新的，从当前数据列表中获取最新状态
    const latestDataList = this.flcTableConfig.dataList;
    const updatedSelectList: ManufactureListItem[] = [];

    data.list.forEach((selectedItem) => {
      const latestItem = latestDataList.find((item) => item.id === selectedItem.id);
      if (latestItem) {
        updatedSelectList.push(latestItem);
      } else {
        // 如果在最新数据中找不到，使用原数据（可能是新数据还未加载完成）
        updatedSelectList.push(selectedItem);
      }
    });

    this.selectList = updatedSelectList;
  }

  // 重置、收缩
  onReset(): void {
    this.searchParams = {};
    this.refresh();
  }

  refresh() {
    this.getSearchOptions();
    this.getList();
  }

  private clearAllSelected() {
    this.flcTable.clearAllSelected();
    this.flcTableConfig = { ...this.flcTableConfig };
  }

  /**
   * 同步更新选中的数据，确保选中数据是最新的
   * 当数据刷新后，需要将selectList中的数据更新为最新的数据
   */
  private syncSelectedListWithLatestData() {
    if (this.selectList.length === 0) {
      return;
    }

    const latestDataList = this.flcTableConfig.dataList;
    const updatedSelectList: ManufactureListItem[] = [];

    // 遍历当前选中的数据，从最新数据中找到对应的项并更新
    this.selectList.forEach((selectedItem) => {
      const latestItem = latestDataList.find((item) => item.id === selectedItem.id);
      if (latestItem) {
        updatedSelectList.push(latestItem);
      } else {
        // 如果在最新数据中找不到，保留原数据（可能是分页或筛选导致的）
        updatedSelectList.push(selectedItem);
      }
    });

    // 更新选中的数据列表
    this.selectList = updatedSelectList;
  }

  onHandleWhere() {
    const searchParams: SearchParams = { ...this.searchParams };
    this.searchList.forEach((item) => {
      if (item.type === 'time') {
        const timeKeys = item.paramsKeys || [];
        const _values = searchParams[item.valueKey] as number[];
        if (searchParams[item.valueKey] && _values.length) {
          const startTime = Number(format(startOfDay(_values[0]), 'T'));
          const endTime = Number(format(endOfDay(_values[1]), 'T'));
          searchParams[timeKeys[0]] = startTime;
          searchParams[timeKeys[1]] = endTime;
          // searchParams[item.valueKey] = [startTime, endTime];
        } else {
          // searchParams[item.valueKey] = [];
          searchParams[timeKeys[0]] = null;
          searchParams[timeKeys[1]] = null;
        }
      }
    });
    this.onSearch(searchParams);
  }

  keywords: string | null = '';
  onInputSearchValue(value: string) {
    this.keywords = value.length ? value : null;
    this.flcTableConfig.pageIndex = 1;
    this.searchParams.code = this.keywords;
    this.getList();
  }

  onAudit(action: 'pass' | 'reject') {
    if (this.isEmpty()) return;
    const _invalidCodes: string[] = [];
    const _ids: number[] = [];
    this.selectList.forEach((item) => {
      item.status !== ManufactureOrderStatusEnum.waitCheck ? _invalidCodes.push(item.code) : _ids.push(item.id);
    });
    if (_invalidCodes.length) {
      this._msg.warning(`结算单号${_invalidCodes.join('、')}不能操作${action === 'pass' ? '审核通过' : '退回修改'}`, { nzDuration: 10000 });
    }
    if (!_ids.length) return;
    action === 'pass' ? this.onPass(_ids) : this.onModify(_ids);
  }

  /**
   * 退回修改
   */
  private onModify(_ids: number[]): void {
    this._modalService.create({
      nzContent: SettlementReturnModalComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
      nzOnOk: (comp) => {
        const _value = comp.formGroup.getRawValue();
        this._service.auditReturn(_ids, _value.reason).subscribe((result) => {
          if (result.data) {
            this._msg.success('退回成功');
            this.clearAllSelected();
            this.refresh();
          }
        });
      },
    });
  }

  private onPass(ids: number[]) {
    const ref = this._flcModalService.confirmCancel({ content: '确定审核通过' });
    ref.afterClose.subscribe((res) => {
      if (res) {
        this._service.pass(ids).subscribe((res) => {
          if (res?.code === 200) {
            this._msg.success('审核通过');
            this.clearAllSelected();
            this.refresh();
          }
        });
      }
    });
  }

  private isEmpty(): boolean {
    if (!this.selectList.length) {
      this._notice.error('请至少选择一条数据', '');
      return true;
    }
    return false;
  }
  // 导出
  showExportTableModal() {
    const exportModalData = {
      version: '1.1',
      tablename: 'export',
      customConfig: JSON.parse(JSON.stringify(exportConfig)),
    };
    this.exportModal.showModel(exportModalData);
  }
  onExport() {
    if (this.isExporting) return;
    const column = this.exportModal.getCheckedList();
    this.isExporting = true;
    const params = {
      ...this.searchParams,
      ids: this.selectList.map((item) => item.id),
      column,
    };
    this._service
      .export(params)
      .pipe(
        finalize(() => {
          this.isExporting = false;
        })
      )
      .subscribe((res: any) => {
        if (res.code === 200) {
          const url: any = res?.data?.url;
          const name = '加工结算单' + '_' + format(new Date(), 'yyyyMMdd');
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.setAttribute('download', name + '.xlsx');
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          this._notice.success('导出成功', '');
          this.resetTableSelect();
        } else {
          this._notice.error('导出失败', '');
        }
      });
  }
  resetTableSelect() {
    this.flcTable.clearAllSelected();
  }
}
