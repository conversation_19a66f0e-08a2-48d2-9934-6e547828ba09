<div class="searchBarWrap" #searchBarWrap>
  <div *ngFor="let item of searchList" class="search-box">
    <span class="search-label">{{ "receivableInquiry." + item.label | translate }}：</span>
    <nz-select
      [nzShowSearch]="true" 
      [nzDisabled]="item.disabled"
      [nzAllowClear]="true"
      [(ngModel)]="searchData[item.valueKey]"
      (ngModelChange)="getList(true)"
      [nzOptions]="searchOption?.[item.labelKey] ?? []"
    >
    </nz-select>
  </div>
</div>
<div nz-row class="select-querytype">
  <nz-radio-group nz-col nzFlex="200px" [ngModel]="queryType" (ngModelChange)="queryTypeChange($event)" nzButtonStyle="solid">
    <label nz-radio-button [nzValue]="1">{{ "receivableInquiry.应收明细" | translate }}</label>
    <label nz-radio-button [nzValue]="2">{{ "receivableInquiry.应收汇总" | translate }}</label>
  </nz-radio-group>
  <label *ngIf="queryType == 1" nz-col nzFlex="auto" nz-checkbox [(ngModel)]="searchData.is_unsettled" (ngModelChange)="getList(true)">{{ "receivableInquiry.未付清" | translate }}</label>
</div>
<div class="table">
  <flc-table
    #tableRef
    style="flex: 1; overflow: hidden"
    [tableHeader]="tableHeader"
    [tableConfig]="tableConfig"
    [template]="tableTemplate"
    (sizeChanges)="onSizeChanges($event)"
    (indexChanges)="onIndexChange($event)">
  </flc-table>
  <ng-template #tableTemplate let-data="data">
    <ng-container *ngIf="data.isTd">
      <ng-container *ngIf="data.key == 'order_code'">
        <flc-table-body-render [data]="_getOrders(data.key, data.item)" type="text"></flc-table-body-render>
      </ng-container>
      <ng-container *ngIf="data.key == 'style_code'">
        <flc-table-body-render [data]="_getOrders(data.key, data.item)" type="text"></flc-table-body-render>
      </ng-container>
    </ng-container>
  </ng-template>
</div>