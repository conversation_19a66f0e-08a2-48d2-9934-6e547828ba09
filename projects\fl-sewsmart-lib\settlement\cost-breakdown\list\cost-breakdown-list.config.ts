export const searchOptions = [
  {
    type: 'input',
    label: '订单号',
    key: 'order_no',
    placeholder: '请输入订单号',
  },
  {
    type: 'input',
    label: '交付单号',
    key: 'delivery_no',
    placeholder: '请输入交付单号',
  },
  {
    type: 'input',
    label: '款号',
    key: 'style_no',
    placeholder: '请输入款号',
  },
  {
    type: 'input',
    label: '品名',
    key: 'product_name',
    placeholder: '请输入品名',
  },
  {
    type: 'select',
    label: '客户',
    key: 'customer_id',
    placeholder: '请选择客户',
    options: [], // 将从 customerOptions 动态填充
  },
];

export const tableHeader = [
  {
    label: '订单号',
    key: 'order_code',
    width: '140px',
    type: 'template',
    templateName: 'order_code',
    visible: true,
    pinned: false,
    disable: false,
  },
  {
    label: '交付单号',
    key: 'po_codes',
    width: '140px',
    type: 'template',
    templateName: 'po_codes',
    visible: true,
    pinned: false,
    disable: false,
  },
  {
    label: '下单日期',
    key: 'order_date',
    width: '120px',
    type: 'template',
    templateName: 'order_date',
    visible: true,
    pinned: false,
    disable: false,
    sort: true,
  },
  {
    label: '交付日期',
    key: 'po_due_dates',
    width: '120px',
    type: 'template',
    templateName: 'po_due_dates',
    visible: true,
    pinned: false,
    disable: false,
  },
  { label: '客户', key: 'customer_name', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '款号', key: 'style_code', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '品名', key: 'category', width: '140px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '订单数量', key: 'order_quantity', width: '100px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '销售数量', key: 'sales_quantity', width: '100px', type: 'text', visible: true, pinned: false, disable: false },
  {
    label: '单价',
    key: 'unit_prices',
    width: '100px',
    type: 'template',
    templateName: 'unit_prices',
    visible: true,
    pinned: false,
    disable: false,
  },
  { label: '金额', key: 'amount', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '币种', key: 'currency_name', width: '80px', type: 'text', visible: true, pinned: false, disable: false },
  {
    label: '汇率',
    key: 'express_rates',
    width: '100px',
    type: 'template',
    templateName: 'express_rates',
    visible: true,
    pinned: false,
    disable: false,
  },
  // { label: '金额（本币）', key: 'amount_local', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '面料入库金额', key: 'fabric_inbound_amount', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '面料领用金额', key: 'fabric_outbound_amount', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '辅料入库金额', key: 'accessories_inbound_amount', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '辅料领用金额', key: 'accessories_outbound_amount', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '物料入库金额', key: 'material_inbound_amount', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '物料领用金额', key: 'material_outbound_amount', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '加工付款金额', key: 'process_payment_amount', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '收款金额（本币）', key: 'receivable_amount_local', width: '140px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '费用分摊', key: 'amortization_amount', width: '100px', type: 'text', visible: true, pinned: false, disable: false },
  { label: '毛利', key: 'gross_margin', width: '120px', type: 'text', visible: true, pinned: false, disable: false },
];
