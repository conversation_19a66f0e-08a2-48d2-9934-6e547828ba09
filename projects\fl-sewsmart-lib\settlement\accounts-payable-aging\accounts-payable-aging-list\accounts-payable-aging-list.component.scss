.searchBarWrap {
  flex-shrink: 0;
  background-color: #fff;
  margin-bottom: 12px;

  padding: 8px 12px;
  font-size: 14px;
  color: #54607c;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  justify-content: flex-start;
  gap: 8px 24px;

  .search-box {
    display: flex;
    align-items: center;
    height: 32px;
  }

  .search-label {
    flex-shrink: 0;
    text-align: right;
  }

  ::ng-deep nz-select {
    min-width: 115px;
    width: 100%;
  }
}

.select-querytype {
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 8px 12px;

  .select-checked {
    margin-left: 12px;
  }
}
.table {
  padding: 0 12px 8px;
  background-color: #fff;

  .isClick {
    color: #4d96ff;
  }
  .isClick:hover {
    cursor: pointer;
  }
}