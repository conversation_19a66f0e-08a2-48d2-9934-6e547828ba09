
/** 列表页面搜索列表 */
export const receivableInquirySearchList = [
  {
    label: '客户',
    labelKey: 'customer',
    valueKey: 'customer_id',
    disabled: false
  },
  {
    label: '发票号',
    labelKey: 'invoice',
    valueKey: 'invoice_code',
    disabled: false
  },
];

/** 应收查询表头 */
export const receivableInquiryTableHeader = [
  { label: '客户', key: 'customer_name'},
  { label: '发票号', key: 'invoice_code'},
  { label: '开票日期', key: 'invoice_date', type: 'date' },
  { label: '币种', key: 'currency_name'},
  { label: '应收金额', key: 'amount_receivable'},
  { label: '已收金额', key: 'amount_received'},
  { label: '剩余应收', key: 'amount_remaining'},
  { label: '订单号', key: 'order_code', type: 'template', templateName: 'order_code',},
  { label: '款号', key: 'style_code', type: 'template', templateName: 'style_code'},
].map((item) => {
  return { type: 'text', width: '110px', visible: true, pinned: false, ...item}
})