import { Component, OnInit, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { receivableInquirySearchList, receivableInquiryTableHeader } from '../models/receivable-inquiry.config';
import { ReceivableInquiryService } from '../receivable-inquiry.service';
import { ReceivableDetailStatementListOption } from '../models/receivable-inquiry.interface';

@Component({
  selector: 'app-receivable-inquiry-list',
  templateUrl: './receivable-inquiry-list.component.html',
  styleUrls: ['./receivable-inquiry-list.component.scss']
})
export class ReceivableInquiryListComponent implements OnInit {
  @ViewChild('searchBarWrap') searchBarWrap!: ElementRef<HTMLElement>;

  constructor(
    private _cdRef: ChangeDetectorRef,
    private _service: ReceivableInquiryService
  ) { }

  // 筛选配置config
  searchList = receivableInquirySearchList;
  /** 筛选项下拉数据 */
  searchOption?: ReceivableDetailStatementListOption
  // 需要筛选的数据
  searchData = this.initSearchData();

  // table表头配置
  tableHeader = receivableInquiryTableHeader;
  // table表格配置
  tableConfig: any = {
    translateName: "receivableInquiry.",
    detailBtn: false,
    dataList: [],
    count: null,
    height: 800,
    loading: false,
    tableName: 'receivableInquiryList',
    hasAction: false,
    pageSize: 20,
    pageIndex: 1,
    actionWidth: '120px',
  };
  /**
   * 查询类型
   * @description 1是应收明细 2是应收汇总
   */
  queryType: 1 | 2 = 1;

  ngOnInit() {
    this.getOptionList()
    this.getList(true)
  }

  ngAfterViewInit() {
    this.calcTableHeight()
  }

  // 初始化请求参数
  initSearchData() {
    return {
      customer_id: null,
      invoice_code: null,
      is_unsettled: true
    }
  }

  // 获取选项下拉数据
  getOptionList() {
    this._service.getOptionList()?.subscribe((res) => {
      if (res.code == 200) {
        this.searchOption = res?.data
      }
    })
  }

  // 类型切换
  queryTypeChange(value: 1 | 2) {
    this.queryType = value
    if (value == 2) {
      const showHeader = ['customer_name', 'currency_name', 'amount_receivable', 'amount_received']
      this.tableHeader = receivableInquiryTableHeader?.filter((item) => showHeader?.includes(item.key))
    } else {
      this.tableHeader = receivableInquiryTableHeader
    }
    this.searchList?.forEach((item) => value == 2 && item.labelKey == 'invoice' ? item.disabled = true : item.disabled = false)
    this.searchData.invoice_code = null
    this.getList(true)
  }

  // 获取当前列表
  getList(reset = false) {
    this.tableConfig = {
      ...this.tableConfig,
      loading: true
    };
    if (reset) {
      this.tableConfig.pageIndex = 1
    }
    const payload: any = {
      ...this.searchData,
      page: this.tableConfig.pageIndex,
      page_size: this.tableConfig.pageSize
    }
    if (this.queryType == 2) {
      delete payload?.is_unsettled;
      delete payload?.invoice_code;
    }
    const _serviceInterface = this.queryType == 1 ? this._service?.getList(payload) : this._service.getSumList(payload)
    _serviceInterface.subscribe((res) => {
      if (res.code == 200) {
        this.tableConfig.loading = false;
        this.tableConfig.dataList = res.data.data;
        this.tableConfig.count = res.data?.total;
        this.tableConfig = { ...this.tableConfig };
      }
    })
  }

  // 计算高度
  calcTableHeight() {
    const bodyHeight = document.querySelector('.ant-layout-content')!.clientHeight;
    const searchBarHeight = this.searchBarWrap?.nativeElement?.clientHeight;
    const renderY = bodyHeight - searchBarHeight;
    const renderScrollY = renderY - 90 - 48 - 12;
    this.tableConfig.height = renderScrollY;
    this.tableConfig = { ...this.tableConfig };
    // 手动触发变更检测
    this._cdRef.detectChanges();
  }


  /**
   * 页数改变
   * @param  {number} e
   */
  onSizeChanges(e: number) {
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = e;
    this.getList();
  }
  /**
  * 页码改变
  * @param  {number} e
  */
  onIndexChange(e: number) {
    this.tableConfig.pageIndex = e;
    this.getList();
  }

  // 获取Orders信息中的对应字段的数据
  _getOrders(key: string, data: any) {
    if (data?.orders?.length) {
      const nameList = data?.orders?.map((item: any) => item?.[key])
      return nameList.join('、')
    }
    return null
  }

}
