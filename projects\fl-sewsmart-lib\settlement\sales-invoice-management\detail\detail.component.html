<flc-app-header [showTemplate]="true" [headerTopTitle]="headerTopTitleTpl" [headerBtn]="headerBtnTmpl">
  <ng-template #headerTopTitleTpl>
    <div class="head-title">
      <span *ngIf="pageMode === PageEditStatusEnum.add">{{ 'salesInvoice.新建销售发票' | translate }}</span>
      <span *ngIf="pageMode === PageEditStatusEnum.edit">{{ 'salesInvoice.编辑销售发票' | translate }}</span>
      <span *ngIf="pageMode === PageEditStatusEnum.read">{{ 'salesInvoice.销售发票详情' | translate }}</span>
      <span class="header-tip" [class]="'status-' + detail?.status" *ngIf="detail?.status">{{ detail.status_name }}</span>
    </div>
  </ng-template>

  <ng-template #headerBtnTmpl>
    <div class="btn-box">
      <!-- 返回按钮 -->
      <button nz-button flButton="default" [nzShape]="'round'" (click)="onBack()">
        {{ 'flss.btn.back' | translate }}
      </button>

      <button
        *ngIf="
          ['add', 'edit'].includes(pageMode) &&
          [OrderStatus.verified, OrderStatus.toModify].includes(orderStatus) &&
          userActions.includes('settlement:sales-invoice-management-update')
        "
        nz-button
        flButton="default"
        [nzShape]="'round'"
        (click)="cancel()">
        {{ 'flss.btn.cancel' | translate }}
      </button>

      <!-- 查看模式按钮 -->
      <ng-container
        *ngIf="
          ['read'].includes(pageMode) &&
          ![OrderStatus.toAudit, OrderStatus.cancelled, OrderStatus.verified].includes(orderStatus) &&
          userActions.includes('settlement:sales-invoice-management-update')
        ">
        <button nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="onSwitchToEdit()">
          <i nz-icon nzType="edit"></i>
          {{ 'flss.btn.edit' | translate }}
        </button>
      </ng-container>
      <!-- 新建/编辑模式按钮 -->
      <ng-container *ngIf="pageMode !== PageEditStatusEnum.read">
        <button
          *ngIf="
            ['add', 'edit'].includes(pageMode) &&
            [OrderStatus.toSubmit, OrderStatus.toModify].includes(orderStatus) &&
            (userActions.includes('settlement:sales-invoice-management-create') ||
              userActions.includes('settlement:sales-invoice-management-update'))
          "
          nz-button
          flButton="pretty-minor"
          [nzShape]="'round'"
          [nzLoading]="saving"
          (click)="onSubmit(1)">
          {{ 'flss.btn.save_temp' | translate }}
        </button>
        <button
          *ngIf="
            ['add', 'edit'].includes(pageMode) &&
            [OrderStatus.toSubmit, OrderStatus.toModify].includes(orderStatus) &&
            (userActions.includes('settlement:sales-invoice-management-create') ||
              userActions.includes('settlement:sales-invoice-management-update'))
          "
          nz-button
          flButton="pretty-primary"
          [nzShape]="'round'"
          [nzLoading]="saving"
          (click)="onSubmit(2)">
          <i nz-icon [nzIconfont]="'icon-tijiao'"></i>
          {{ 'flss.btn.commit' | translate }}
        </button>
      </ng-container>

      <ng-container
        *ngIf="
          ['read'].includes(pageMode) &&
          [OrderStatus.toAudit, OrderStatus.toModifyAudit].includes(orderStatus) &&
          userActions.includes('settlement:sales-invoice-management-approval')
        ">
        <button nz-button flButton="fault-minor" [nzShape]="'round'" (click)="modify()" [flcDisableOnClick]="1000">
          {{ 'btn.modify' | translate }}
        </button>

        <button nz-button flButton="pretty-primary" [nzShape]="'round'" (click)="pass()">
          {{ 'btn.pass' | translate }}
        </button>
      </ng-container>

      <!-- <button
        *ngIf="['read'].includes(pageMode) && [OrderStatus.verified].includes(orderStatus)"
        nz-button
        flButton="pretty-primary"
        [nzShape]="'round'"
        (click)="payee()">
        {{ 'flss.btn.payee' | translate }}
      </button> -->
    </div>
  </ng-template>
</flc-app-header>

<nz-spin [nzSpinning]="loading">
  <form nz-form [formGroup]="salesForm" class="receipt-form">
    <!-- 退回修改 -->
    <div
      *ngIf="[OrderStatus.toModify, OrderStatus.cancelled].includes(orderStatus) && (detail?.return_reason || detail?.cancel_reason)"
      class="reason-container">
      <i nz-icon [nzIconfont]="'icon-cuowu'" class="icon-cuowu"></i>
      <span>审核未通过 </span>
      <flc-text-truncated [data]="'原因：' + detail?.cancel_reason || detail?.return_reason"></flc-text-truncated>
    </div>
    <!-- 基本信息 -->
    <div class="form-section">
      <!-- <div class="section-title">基本信息</div> -->
      <div class="form-content" *ngIf="pageMode === PageEditStatusEnum.edit || pageMode === PageEditStatusEnum.add">
        <div nz-row [nzGutter]="24">
          <div
            nz-col
            [nzSpan]="formItem.itemSpan"
            *ngFor="let formItem of formConfig"
            [style.display]="formItem.key === 'billing_dept_name' && !showDeptField ? 'none' : 'block'">
            <nz-form-item>
              <nz-form-label [nzRequired]="formItem.required" [nzSpan]="formItem.labelSpan">{{
                translateName + formItem.label | translate
              }}</nz-form-label>
              <nz-form-control [nzSpan]="formItem.controlSpan" [flcErrorTip]="translateName + formItem.label | translate">
                <!-- 普通输入框 -->
                <ng-container *ngIf="formItem.type === 'input'">
                  <input
                    nz-input
                    [formControlName]="formItem.key"
                    [placeholder]="formItem.placeholder || ('flss.placeholder.input' | translate)"
                    [maxlength]="formItem.maxLength" />
                </ng-container>
                <ng-container *ngIf="formItem.type === 'select'">
                  <nz-select
                    nzAllowClear
                    [nzShowSearch]="true"
                    [formControlName]="formItem.key"
                    [nzPlaceHolder]="formItem.placeholder || ('flss.placeholder.select' | translate)"
                    [nzDisabled]="formItem.disabled"
                    style="width: 100%"
                    (ngModelChange)="onSelectChange($event, formItem)">
                    <nz-option
                      [nzValue]="option[formItem.valueKey]"
                      [nzLabel]="option[formItem.labelKey]"
                      [nzDisabled]="option.disable"
                      *ngFor="let option of this[formItem.optionKey]">
                    </nz-option>
                  </nz-select>
                </ng-container>
                <!-- 树形选择器 -->
                <ng-container *ngIf="formItem.type === 'department-select'"
                  ><flss-department-select
                    style="width: 100%"
                    [requestInside]="!organizationOptions?.length"
                    [treeOptions]="organizationOptions"
                    [formControlName]="formItem.key"
                    [canSelectEmployee]="true"
                    [canSelectDepartment]="false"
                    [selectCurrentUser]="editMode === PageEditStatusEnum.new"
                    (onSelectChange)="onDapartmentChange($event)">
                  </flss-department-select
                ></ng-container>
                <ng-container *ngIf="formItem.type === 'date'">
                  <nz-date-picker
                    [nzFormat]="'yyyy/MM/dd'"
                    [formControlName]="formItem.key"
                    [nzPlaceHolder]="formItem.placeholder || ('flss.placeholder.select' | translate)"
                    [nzDisabled]="formItem.disabled"
                    style="width: 100%"></nz-date-picker>
                </ng-container>
                <ng-container *ngIf="formItem.type === 'input-number'">
                  <!-- 税率字段特殊处理：直接显示格式化的税率文本 -->
                  <ng-container *ngIf="formItem.key === 'tax_rate'">
                    <flc-text-truncated [data]="formatTaxRateDisplay(detail?.tax_rate || salesForm.get('tax_rate')?.value)"></flc-text-truncated>
                  </ng-container>
                  <!-- 其他数字输入框正常处理 -->
                  <ng-container *ngIf="formItem.key !== 'tax_rate'">
                    <nz-input-number-group [nzAddOnAfter]="formItem.suffix" style="width: 100%">
                      <nz-input-number
                        [formControlName]="formItem.key"
                        [nzMin]="formItem.min"
                        [nzMax]="formItem.max"
                        [nzPrecision]="formItem.precision"
                        [nzDisabled]="formItem.disabled"
                        [nzPlaceHolder]="formItem.placeholder || ('flss.placeholder.input' | translate)"
                        style="width: 100%"></nz-input-number>
                    </nz-input-number-group>
                  </ng-container>
                </ng-container>
                <ng-container *ngIf="formItem.type === 'input-area'">
                  <nz-textarea-count [nzMaxCharacterCount]="formItem.maxlength" class="inline-count">
                    <textarea
                      [nzAutosize]="{ minRows: 1, maxRows: formItem.maxRows }"
                      [formControlName]="formItem.key"
                      nz-input
                      [maxlength]="formItem.maxlength"></textarea>
                  </nz-textarea-count>
                </ng-container>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
      </div>

      <div class="form-content" *ngIf="pageMode === PageEditStatusEnum.read">
        <div nz-row [nzGutter]="24">
          <div
            nz-col
            [nzSpan]="formItem.itemSpan"
            *ngFor="let formItem of formConfig"
            [style.display]="formItem.key === 'billing_dept_name' && !showDeptField ? 'none' : 'block'">
            <nz-form-item>
              <nz-form-label [nzRequired]="formItem.required" [nzSpan]="formItem.labelSpan">{{
                translateName + formItem.label | translate
              }}</nz-form-label>
              <nz-form-control [nzSpan]="formItem.controlSpan">
                <!-- 发票类型显示 -->
                <ng-container *ngIf="formItem.key === 'invoice_type'">
                  <flc-text-truncated [data]="detail?.invoice_type_name"></flc-text-truncated>
                </ng-container>
                <!-- 客户显示 -->
                <ng-container *ngIf="formItem.key === 'customer_id'">
                  <flc-text-truncated [data]="detail?.customer_name"></flc-text-truncated>
                </ng-container>
                <!-- 开票方式显示 -->
                <ng-container *ngIf="formItem.key === 'billing_method'">
                  <flc-text-truncated [data]="detail?.billing_method_name"></flc-text-truncated>
                </ng-container>
                <!-- 开票人显示 -->
                <ng-container *ngIf="formItem.key === 'billing_user_id'">
                  <flc-text-truncated [data]="detail?.billing_user_name"></flc-text-truncated>
                </ng-container>
                <!-- 部门显示 -->
                <ng-container *ngIf="formItem.key === 'billing_dept_name'">
                  <flc-text-truncated [data]="detail?.billing_dept_name"></flc-text-truncated>
                </ng-container>
                <!-- 币种显示 -->
                <ng-container *ngIf="formItem.key === 'currency_id'">
                  <flc-text-truncated [data]="detail?.currency_name"></flc-text-truncated>
                </ng-container>
                <!-- 税率显示 -->
                <ng-container *ngIf="formItem.key === 'tax_rate'">
                  <flc-text-truncated [data]="formatTaxRateDisplay(detail?.tax_rate)"></flc-text-truncated>
                </ng-container>
                <!-- 日期显示 -->
                <ng-container *ngIf="formItem.type === 'date'">
                  <flc-text-truncated
                    [data]="detail?.[formItem.key] ? (detail?.[formItem.key] | date: 'yyyy/MM/dd') : null"></flc-text-truncated>
                </ng-container>
                <!-- 其他字段显示 -->
                <ng-container
                  *ngIf="
                    formItem.type !== 'date' &&
                    ![
                      'invoice_type',
                      'customer_id',
                      'billing_method',
                      'billing_user_id',
                      'billing_dept_name',
                      'currency_id',
                      'tax_rate'
                    ].includes(formItem.key)
                  ">
                  <flc-text-truncated [data]="detail?.[formItem.key]"></flc-text-truncated>
                </ng-container>
              </nz-form-control>
            </nz-form-item>
          </div>
        </div>
      </div>
    </div>

    <!-- 关联信息 -->
    <!-- 关联信息 -->
    <div class="form-section">
      <div class="section-title">关联信息</div>
      <div class="table-container">
        <nz-table
          #detailTable
          [nzData]="flatDetailList"
          [nzScroll]="{ x: '100%' }"
          [nzShowPagination]="false"
          [nzFrontPagination]="false"
          [nzLoading]="loading"
          nzBordered>
          <thead>
            <tr>
              <th nzLeft nzWidth="36px">
                <a class="setting-btn" [ngStyle]="{ color: btnHighLight ? '#4D96FF' : '' }" (click)="changeHeader($event)">
                  <i nz-icon nzType="setting" nzTheme="fill"></i>
                </a>
              </th>

              <ng-container *ngFor="let item of detailTableHeader">
                <th
                  [nzLeft]="item.disable || item.pinned"
                  nz-resizable
                  *ngIf="item.visible"
                  nzPreview
                  [nzWidth]="item.width"
                  (nzResizeEnd)="onResize($event, item.label)">
                  {{ translateName + item.label | translate }}
                  <i
                    [nzPopoverOverlayClassName]="'tips-bg'"
                    *ngIf="item.tips"
                    class="hint-icon"
                    nz-popover
                    nzPopoverTitle=""
                    [nzPopoverContent]="item.tips"
                    nzPopoverTrigger="hover"
                    [nzPopoverBackdrop]="true"
                    nz-icon
                    [nzIconfont]="'icon-tishi'"></i>
                  <div class="resize-trigger"></div>
                  <nz-resize-handle nzDirection="right">
                    <div class="resize-trigger"></div>
                  </nz-resize-handle>
                </th>
              </ng-container>

              <!-- 操作列 -->
              <th *ngIf="pageMode === PageEditStatusEnum.edit || pageMode === PageEditStatusEnum.add" nzWidth="80px">操作</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of flatDetailList; index as i">
              <td
                nzLeft="0px"
                [attr.rowspan]="shouldShowRowNumber(i) ? getOutboundCodeRowSpan(i) : null"
                [style.display]="shouldShowRowNumber(i) ? 'table-cell' : 'none'"
                style="vertical-align: middle; text-align: center">
                <ng-container *ngIf="shouldShowRowNumber(i)">
                  {{ getRowNumber(i) }}
                </ng-container>
              </td>
              <ng-container *ngFor="let col of detailTableHeader">
                <!-- 出库单号需要跨行合并 -->
                <td
                  [nzLeft]="col.disable || col.pinned"
                  *ngIf="col.visible && col.key === 'outbound_code'"
                  [attr.rowspan]="shouldShowOutboundCode(i) ? getOutboundCodeRowSpan(i) : null"
                  [style.display]="shouldShowOutboundCode(i) ? 'table-cell' : 'none'"
                  style="vertical-align: middle">
                  <flc-text-truncated [data]="item.outbound_code"></flc-text-truncated>
                </td>

                <!-- 订单号需要跨行合并 -->
                <td
                  [nzLeft]="col.disable || col.pinned"
                  *ngIf="col.visible && col.key === 'order_code'"
                  [attr.rowspan]="shouldShowOrderCode(i) ? getOrderCodeRowSpan(i) : null"
                  [style.display]="shouldShowOrderCode(i) ? 'table-cell' : 'none'"
                  style="vertical-align: middle">
                  <flc-text-truncated [data]="item.order_code"></flc-text-truncated>
                </td>

                <!-- 交付单号需要跨行合并 -->
                <td
                  [nzLeft]="col.disable || col.pinned"
                  *ngIf="col.visible && col.key === 'po_code'"
                  [attr.rowspan]="shouldShowPoCode(i) ? getPoCodeRowSpan(i) : null"
                  [style.display]="shouldShowPoCode(i) ? 'table-cell' : 'none'"
                  style="vertical-align: middle">
                  <flc-text-truncated [data]="item.po_code"></flc-text-truncated>
                </td>

                <!-- 款号和品名需要跨行合并 -->
                <td
                  [nzLeft]="col.disable || col.pinned"
                  *ngIf="col.visible && ['style_code', 'category'].includes(col.key)"
                  [attr.rowspan]="shouldShowStyleAndCategory(i) ? getStyleCategoryRowSpan(i) : null"
                  [style.display]="shouldShowStyleAndCategory(i) ? 'table-cell' : 'none'"
                  style="vertical-align: middle">
                  <!-- 款号 -->
                  <ng-container *ngIf="col.key === 'style_code'">
                    <flc-text-truncated [data]="item.style_code"></flc-text-truncated>
                  </ng-container>

                  <!-- 品名 -->
                  <ng-container *ngIf="col.key === 'category'">
                    <flc-text-truncated [data]="item.category"></flc-text-truncated>
                  </ng-container>
                </td>

                <!-- 其他字段正常显示 -->
                <td
                  [nzLeft]="col.disable || col.pinned"
                  *ngIf="col.visible && !['outbound_code', 'order_code', 'po_code', 'style_code', 'category'].includes(col.key)">
                  <!-- 应收单号 -->
                  <ng-container *ngIf="col.key === 'bills_receivable_code'">
                    <flc-text-truncated [data]="item.bills_receivable_code"></flc-text-truncated>
                  </ng-container>

                  <!-- 颜色 -->
                  <ng-container *ngIf="col.key === 'color_name'">
                    <flc-text-truncated [data]="item.color_name"></flc-text-truncated>
                  </ng-container>

                  <!-- 尺码 -->
                  <ng-container *ngIf="col.key === 'spec_size'">
                    <flc-text-truncated [data]="item.spec_size"></flc-text-truncated>
                  </ng-container>

                  <!-- 数量 -->
                  <ng-container *ngIf="col.key === 'qty'">
                    <span>
                      {{ item.qty | number: '1.0-0' }}
                    </span>
                  </ng-container>

                  <!-- 不含税单价（保留3位小数） -->
                  <ng-container *ngIf="col.key === 'exclude_tax_price'">
                    <flc-text-truncated [data]="item[col.key] | number: '1.3-3'"></flc-text-truncated>
                  </ng-container>

                  <!-- 其他金额字段（保留2位小数） -->
                  <ng-container
                    *ngIf="
                      [
                        'price',
                        'total_money',
                        'receivable_money',
                        'exclude_tax_money',
                        'exclude_tax_local',
                        'tax_amount',
                        'tax_amount_local',
                        'receivable_money_local'
                      ].includes(col.key)
                    ">
                    <flc-text-truncated [data]="item[col.key] | number: '1.2-2'"></flc-text-truncated>
                  </ng-container>
                </td>
              </ng-container>

              <!-- 操作列 -->
              <td
                *ngIf="pageMode === PageEditStatusEnum.edit || pageMode === PageEditStatusEnum.add"
                [attr.rowspan]="shouldShowActionButton(i) ? getOutboundRowSpan(i) : null"
                [style.display]="shouldShowActionButton(i) ? 'table-cell' : 'none'"
                style="vertical-align: middle; text-align: center">
                <button
                  *ngIf="shouldShowActionButton(i)"
                  nz-button
                  nzType="link"
                  flButton="link"
                  nz-tooltip
                  nzTooltipTitle="删除出库单"
                  (click)="onDeleteOutbound(item)">
                  <i nz-icon nzIconfont="icon-caozuolan_shanchu1" class="delete-hover"></i>
                </button>
              </td>
            </tr>
          </tbody>

          <!-- 总计行 -->
          <tfoot>
            <tr style="text-align: center" class="summary-row">
              <td nzLeft="0px"></td>
              <ng-container *ngFor="let col of detailTableHeader">
                <td [nzLeft]="col.disable || col.pinned" *ngIf="col.visible">
                  <ng-container *ngIf="col.key === 'outbound_code'">
                    <strong>合计</strong>
                  </ng-container>
                  <ng-container *ngIf="col.key === 'qty'">
                    {{ detailSummary.totalQty | number: '1.0-0' }}
                  </ng-container>
                  <ng-container *ngIf="col.key === 'total_money'">
                    {{ detailSummary.totalMoney | number: '1.2-2' }}
                  </ng-container>
                  <ng-container *ngIf="col.key === 'receivable_money'">
                    {{ detailSummary.totalReceivableMoney | number: '1.2-2' }}
                  </ng-container>
                  <ng-container *ngIf="col.key === 'exclude_tax_money'">
                    {{ detailSummary.totalExcludeTaxMoney | number: '1.2-2' }}
                  </ng-container>
                  <ng-container *ngIf="col.key === 'exclude_tax_local'">
                    {{ detailSummary.totalExcludeTaxLocal | number: '1.2-2' }}
                  </ng-container>
                  <ng-container *ngIf="col.key === 'tax_amount'">
                    {{ detailSummary.totalTaxAmount | number: '1.2-2' }}
                  </ng-container>
                  <ng-container *ngIf="col.key === 'tax_amount_local'">
                    {{ detailSummary.totalTaxAmountLocal | number: '1.2-2' }}
                  </ng-container>
                  <ng-container *ngIf="col.key === 'receivable_money_local'">
                    {{ detailSummary.totalReceivableMoneyLocal | number: '1.2-2' }}
                  </ng-container>
                </td>
              </ng-container>

              <!-- 操作列总计 -->
              <td *ngIf="pageMode === PageEditStatusEnum.edit || pageMode === PageEditStatusEnum.add">共 {{ flatDetailList.length }} 条</td>
            </tr>
          </tfoot>
        </nz-table>
      </div>
    </div>
  </form>
</nz-spin>
