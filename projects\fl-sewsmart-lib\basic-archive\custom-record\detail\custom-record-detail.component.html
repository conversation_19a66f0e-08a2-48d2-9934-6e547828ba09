<div class="custom-container">
  <flc-app-header
    style="width: 100%"
    [headerTopTitle]="translateName + ('基础档案' + title) | translate"
    [headerBtn]="headerBtnTmpl"></flc-app-header>
  <ng-template #headerBtnTmpl>
    <ng-container *ngFor="let btn of btnConfig">
      <button
        nz-button
        *ngIf="btnList.includes(btn.label)"
        [nzShape]="env_project === 'deepflow' ? null : 'round'"
        [nzDanger]="btn.danger"
        [flButton]="btn.type"
        (click)="onClickBtn(btn.label)"
        [flcDisableOnClick]="1000">
        <i *ngIf="btn.icon" nz-icon [nzIconfont]="btn.icon"></i>
        {{ 'flss.btn.' + btn.label | translate }}
      </button>
    </ng-container>
  </ng-template>

  <div class="inner-container">
    <form [formGroup]="paramsForm" nz-row>
      <div nz-col [nzSpan]="item.nzSpan" *ngFor="let item of categoryFormConfig">
        <nz-form-item nz-row [nzSpan]="24" class="form-item">
          <nz-form-label [ngClass]="lang === 'en' ? 'info-label-en' : 'info-label'" [nzRequired]="item.require">
            {{ translateName + item.label | translate }}
            <span
              *ngIf="item.tooltip"
              nz-icon
              nzType="exclamation-circle"
              style="margin: 0 3px; color: red"
              nz-tooltip
              [nzTooltipTitle]="translateName + item.tooltip | translate"
              nzTheme="outline"></span>
          </nz-form-label>
          <nz-form-control class="info-control" *ngIf="isDetail">
            <ng-containe [ngSwitch]="item.value">
              <ng-container *ngSwitchCase="'status'">
                <flc-text-truncated
                  [data]="'flss.btn.' + (detailData[item.value] === 1 ? '启用' : '禁用') | translate"></flc-text-truncated>
              </ng-container>
              <ng-container *ngSwitchCase="'receiver_country_name'">
                <flc-text-truncated [data]="receiver_country"></flc-text-truncated>
              </ng-container>
              <ng-container *ngSwitchDefault>
                <flc-text-truncated [data]="detailData[item.value]"></flc-text-truncated>
              </ng-container>
            </ng-containe>
          </nz-form-control>

          <nz-form-control class="info-control" *ngIf="!isDetail" [flcErrorTip]="translateName + item.label | translate">
            <ng-container *ngIf="item.type === 'input'">
              <div class="form-item-input-custom">
                <nz-input-group [nzSuffix]="(paramsForm?.get(item?.code)?.value?.length || 0) + '/' + item?.maxlength">
                  <input
                    flcInputTrim
                    nz-input
                    [maxlength]="item.maxlength"
                    [formControlName]="item.code"
                    [placeholder]="'flss.placeholder.input' | translate" />
                </nz-input-group>
              </div>
            </ng-container>

            <ng-container *ngIf="item.type === 'textarea'">
              <nz-textarea-count [nzMaxCharacterCount]="item.maxlength" class="inline-count">
                <textarea
                  nz-input
                  [formControlName]="item.code"
                  [placeholder]="'flss.placeholder.input' | translate"
                  [maxLength]="item.maxlength"
                  [nzAutosize]="{ minRows: 1, maxRows: 3 }"
                  flcInputTrim></textarea>
              </nz-textarea-count>
            </ng-container>

            <ng-container *ngIf="item.type === 'select'">
              <flc-dynamic-search-select
                *ngIf="item.code !== 'related_user_ids'"
                class="reset-select-width"
                [optAlwaysReload]="true"
                [isShowInactiveIcon]="true"
                [dataUrl]="searchOptionFetchUrl"
                [column]="item.columnkey"
                [payLoad]="{ parent_id: 0 }"
                [formControlName]="item.code">
              </flc-dynamic-search-select>

              <flc-dynamic-search-select
                *ngIf="item.code === 'related_user_ids'"
                [optAlwaysReload]="true"
                [cpnMode]="'multiple'"
                [dataUrl]="item.dataUrl"
                [column]="item.columnkey"
                [formControlName]="item.code"
                [customerOptionTpl]="customerOptionTpl"
                [defaultValue]="defaultSysAccountValue ?? []"
                [transData]="{ value: 'user_id', label: 'name' }"
                (handleSearch)="onHandleSysAccountSearch($event)">
                <ng-template #customerOptionTpl let-data="data">
                  {{ data.item.name + '(' + data.item.login_name + ')' }}
                </ng-template>
              </flc-dynamic-search-select>
            </ng-container>

            <ng-container *ngIf="item.type === 'dynamic-select'">
              <flc-dynamic-search-select
                class="reset-select-width"
                [optAlwaysReload]="true"
                [isShowInactiveIcon]="true"
                [dataUrl]="item.dataUrl"
                [column]="item.columnkey"
                [formControlName]="item.code"
                [defaultValue]="{ value: paramsForm.get(item.code)?.value, label: paramsForm.get(item.value)?.value }"
                (handleSearch)="onHandleSearch($event, item)">
              </flc-dynamic-search-select>
            </ng-container>

            <ng-container *ngIf="item.type === 'number'">
              <nz-input-number
                [formControlName]="item.code"
                style="width: 100%"
                [nzPlaceHolder]="'flss.placeholder.input' | translate"
                [nzMin]="item.min"
                [nzMax]="item.max"
                [nzPrecision]="item.precision"></nz-input-number>
            </ng-container>

            <ng-container *ngIf="item.type === 'switch'">
              <nz-switch
                [formControlName]="item.code"
                [nzCheckedChildren]="'flss.btn.启用' | translate"
                [nzUnCheckedChildren]="'flss.btn.禁用' | translate"></nz-switch>
            </ng-container>
            <ng-container *ngIf="item.type === 'cascader'">
              <nz-cascader
                style="width: 100%"
                [formControlName]="item.code"
                [nzOptions]="item.options"
                [nzPlaceHolder]="'flss.placeholder.input' | translate"
                [nzShowSearch]="false"></nz-cascader>
            </ng-container>

            <ng-container *ngIf="item.type === 'local-select'">
              <nz-select [formControlName]="item.code" [nzPlaceHolder]="'flss.placeholder.select' | translate" nzAllowClear nzShowSearch>
                <nz-option *ngFor="let item of item.options" [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
              </nz-select>
            </ng-container>
          </nz-form-control>
        </nz-form-item>
      </div>
    </form>
  </div>

  <!-- 额度详情 -->
  <div style="width: 100%; height: 40px">
    <span style="line-height: 40px; font-weight: 500; color: #515661; font-size: 16px">{{ translateName + '额度详情' | translate }}</span>
  </div>

  <!-- 币种列表 -->
  <div class="currency-quota-container">
    <!-- 币种表格 -->

    <flc-table #currencyTable [tableHeader]="currencyTableHeader" [tableConfig]="currencyTableConfig" [template]="currencyTemplate">
    </flc-table>

    <ng-template #currencyTemplate let-data="data">
      <ng-container *ngIf="data.isTd && data.key === 'amount'">
        <!-- 详情模式：只读显示 -->
        <flc-text-truncated *ngIf="isDetail" [data]="data.item.amount"></flc-text-truncated>
        <!-- 编辑模式：可编辑输入框 -->
        <nz-input-number
          *ngIf="!isDetail"
          [ngModel]="data.item.amount"
          (ngModelChange)="onQuotaAmountChange(data.item.currency_id, $event)"
          [nzMin]="0"
          [nzMax]="999999999999.99"
          [nzPrecision]="2"
          [nzPlaceHolder]="translateName + '请输入' | translate"
          style="width: 100%">
        </nz-input-number>
      </ng-container>
    </ng-template>
  </div>
</div>
