export interface CostBreakdownListParams {
  page?: string;
  page_size?: string;
  keyword?: string;
  order_date_start?: string; // 下单开始日期
  order_date_end?: string; // 下单结束日期
  order_code?: string; // 订单号（下拉精准匹配或模糊搜索）
  po_code?: string; // 交付单号（下拉精准匹配或模糊搜索）
  style_code?: string; // 款号（下拉精准匹配或模糊搜索）
  category?: string; // 品名（下拉精准匹配或模糊搜索）
  customer?: string; // 客户（下拉精准匹配）
  order_by?: string[]; // 排序字段
}

export interface CostAnalysisListReplyOrderPoPoLine {
  quantity?: string; // 交付明细数量
  unit_price?: string; // 交付明细单价
}

export interface CostAnalysisListReplyOrderPo {
  code?: string; // 交付单号
  due_date?: string; // 交付日期
  po_lines?: CostAnalysisListReplyOrderPoPoLine[];
  quantity?: string; // 交付数量
}

export interface CostBreakdownItem {
  order_date?: string; // 下单日期
  order_id?: string; // 订单ID
  order_code?: string; // 订单号
  order_uuid?: string; // 订单UUID
  bulk_order_code?: string; // 订单需求code
  io_uuid?: string; // 订单需求io_uuid
  customer_id?: string; // 客户
  customer_name?: string; // 客户名称
  style_code?: string; // 款号
  category?: string; // 品名
  order_quantity?: string; // 订单数量
  sales_quantity?: string; // 销售数量
  amount?: string; // 金额
  currency_id?: string; // 币种ID
  currency_name?: string; // 币种名称
  express_rates?: string[]; // 汇率
  amount_local?: string; // 金额(本币)
  fabric_inbound_amount?: string; // 面料入库金额
  fabric_outbound_amount?: string; // 面料领用金额
  accessories_inbound_amount?: string; // 辅料入库金额
  accessories_outbound_amount?: string; // 辅料领用金额
  material_inbound_amount?: string; // 物料入库金额
  material_outbound_amount?: string; // 物料领用金额
  process_payment_amount?: string; // 加工付款金额
  receivable_amount?: string; // 收款金额
  receivable_amount_local?: string; // 收款金额(本币)
  amortization_amount?: string; // 费用分摊
  gross_margin?: string; // 毛利
  po_codes?: string[]; // 交付单号
  po_due_dates?: string[]; // 交付日期
  unit_prices?: string[]; // 交付单价
  pos?: CostAnalysisListReplyOrderPo[]; // 交付单信息
}

export interface CostBreakdownListResponse {
  data: CostBreakdownItem[];
  total: string;
}

export interface MaterialInfo {
  material_type: number; // 物料类型（1-面料，2-辅料）
  material_type_name: string; // 物料类型名称
  material_code: string; // 物料编码
  material_name: string; // 物料名称
  material_color_code: string; // 物料颜色/色号
  specification: string; // 规格
  supplier: string; // 供应商名称

  purchase_qty_base: string; // 采购数量（基本单位）
  purchase_qty_purchase: string; // 采购数量（采购单位）
  purchase_amount: string; // 采购金额

  inbound_qty_base: string; // 入库数量（基本单位）
  inbound_qty_purchase: string; // 入库数量（采购单位）
  inbound_amount: string; // 入库金额
  inbound_warehouse: string; // 入库仓库名称

  outbound_qty_base: string; // 出库数量（基本单位）
  outbound_qty_purchase: string; // 出库数量（采购单位）
  outbound_amount: string; // 出库金额

  payment_amount: string; // 已付款金额

  base_unit: string; // 基本单位
  purchase_unit: string; // 采购单位
}

export interface MaterialInfoRequest {
  order_uuid: string;
  material_types: number[]; // 1-面料 2-辅料
}

export interface MaterialInfoResponse {
  material_list: MaterialInfo[];
}

export interface ProcessingInfo {
  bulk_code?: string; // 大货单号code
  factory_code?: string; // 加工厂code
  factory_id?: string; // 加工厂id
  factory_name?: string; // 加工厂name
  factory_stage?: string[]; // 工段列表
  io_uuid?: string; // 大货单号uuid
  outbound_num?: string; // 出库数
  payment_amount?: string; // 付款金额
  po_code?: string[]; // 交付单号
  po_due_time?: string[]; // 交期
  processing_num?: string; // 加工数量
  settlement_money?: string; // 结算金额
  settlement_num?: string; // 结算数
  settlement_price?: string[]; // 结算单价
  store_num?: string; // 入库数
  tax_price?: string; // 含税单价
  temp_money?: string; // 暂估金额
}

export interface ProcessingInfoRequest {
  bulk_order_code?: string; // 订单需求code
  io_uuid?: string; // 订单需求io_uuid
  order_id?: string; // 订单需求id
}

export interface ProcessingInfoResponse {
  data_list?: ProcessingInfo[]; // 列表数据
}

export interface PaymentInfo {
  customer: string; // 客户名称
  outbound_qty: string; // 出库数量
  unit_price: string; // 单价
  received_amount: string; // 收款金额
  unreceived_amount: string; // 未收金额
  currency: string; // 币种
}

export interface PaymentInfoRequest {
  order_uuid: string;
}

export interface ExpenseAllocation {
  type_name: string; // 费用类型
  classification: string; // 分类
  project: string; // 项目
  sec_project: string; // 二级项目
  thr_project: string; // 三级项目
  amortization_amount: string; // 分摊金额
  gen_time: string; // 生成时间
}

export interface ExpenseAllocationRequest {
  order_uuid: string;
}

export interface ExpenseAllocationResponse {
  data: ExpenseAllocation[];
  total: string;
}

export interface CostBreakdownDetail {
  id: number;
  order_info: CostBreakdownItem;
  material_info: MaterialInfo[];
  processing_info: ProcessingInfo[];
  payment_info: PaymentInfo;
  expense_allocation: ExpenseAllocation[];
}

export enum MaterialType {
  FABRIC = 'fabric',
  ACCESSORY = 'accessory',
}

export interface Option {
  label: string;
  value: string;
}

export interface CostBreakdownListOptions {
  order_code: Option[];
  po_code: Option[];
  style_code: Option[];
  category: Option[];
  customer: Option[];
}

export interface CustomerOption {
  value: number;
  label: string;
}
