<div class="searchBarWrap" #searchBarWrap>
  <ng-container *ngFor="let item of searchList">
    <div *ngIf="item.isShow" class="search-box">
      <span class="search-label">{{ "accountsPayableAging." + item.label | translate }}：</span>
      <nz-select [nzAllowClear]="true" nzShowSearch [(ngModel)]="searchData[item.valueKey]"
        (ngModelChange)="searchDataChange(item.valueKey)" [nzOptions]="searchOption?.[item.labelKey] ?? []">
      </nz-select>
    </div>
  </ng-container>
</div>
<div class="select-querytype">
  <nz-radio-group nz-col [ngModel]="queryType" (ngModelChange)="queryTypeChange($event)" nzButtonStyle="solid">
    <label nz-radio-button [nzValue]="1">{{ "accountsPayableAging.应付明细" | translate }}</label>
    <label nz-radio-button [nzValue]="2">{{ "accountsPayableAging.应付汇总" | translate }}</label>
  </nz-radio-group>
  <label class="select-checked" *ngIf="queryType == 1" nz-checkbox [ngModel]="searchData?.account_type?.includes(1)"
    (ngModelChange)="checkAccountTypeChange($event, 1)">{{ "accountsPayableAging.物料" | translate }}</label>
  <label *ngIf="queryType == 1" nz-checkbox [ngModel]="searchData?.account_type?.includes(2)"
    (ngModelChange)="checkAccountTypeChange($event, 2)">{{ "accountsPayableAging.加工" | translate }}</label>
  <nz-divider nzType="vertical"></nz-divider>
  <label *ngIf="queryType == 1" nz-checkbox [(ngModel)]="searchData.unpaid_money" (ngModelChange)="getList(true)">{{
    "accountsPayableAging.未付清" | translate }}</label>
</div>
<div class="table">
  <nz-table [nzData]="tableConfig.dataList" [nzFrontPagination]="false" [nzTotal]="tableConfig.count ?? 0"
    [nzLoading]="tableConfig.loading" [nzPageIndex]="tableConfig.pageIndex" [nzPageSize]="tableConfig.pageSize"
    [nzShowPagination]="true" [nzShowSizeChanger]="true" nzPaginationType="small" [nzBordered]="true"
    (nzPageSizeChange)="onSizeChanges($event)" (nzPageIndexChange)="onIndexChange($event)"
    [nzScroll]="{ x: '100%', y: tableConfig.height + 'px' }" [nzPageSizeOptions]="[20, 30, 40, 50]">
    <thead>
      <tr>
        <th nzLeft nzWidth="42px">#</th>
        <ng-container *ngFor="let item of tableHeader">
          <th [nzLeft]="item.disable || item.pinned" nz-resizable *ngIf="item.visible" nzPreview [nzWidth]="item.width"
            [nzMaxWidth]="360" [nzMinWidth]="56" (nzResizeEnd)="onResize($event, item.label)">
            {{ item.label | translate }}
            <nz-resize-handle nzDirection="right">
              <div class="resize-trigger"></div>
            </nz-resize-handle>
          </th>
        </ng-container>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let data of tableConfig.dataList; let i = index">
        <ng-container *ngIf="queryType == 1">
          <tr *ngFor="let item of data.payable_dates; let j = index">
            <td nzLeft *ngIf="j == 0" [attr.rowspan]="data.payable_dates?.length">{{ i+1 }}</td>
            <ng-container *ngFor="let col of tableHeader">
              <ng-container *ngIf="col.visible">
                <ng-container [ngSwitch]="true">
                  <ng-container *ngSwitchCase="[
                      'account_type',
                      'supplier_name',
                      'contract_number',
                      'contract_money',
                      'invoice_money',
                      'no_invoice_money',
                    ].includes(col.key)">
                    <td *ngIf="j == 0" [attr.rowspan]="data.payable_dates?.length" [nzLeft]="col.disable || col.pinned"
                      (click)="queryRowData(col.key, data)">
                      <flc-table-body-render [ngClass]="{isClick: col.key == 'contract_number'}"
                        [data]="getColName(col.key, data)" [type]="col.type"></flc-table-body-render>
                    </td>
                  </ng-container>
                  <td *ngSwitchDefault [nzLeft]="col.disable || col.pinned">
                    <flc-table-body-render [data]="data?.[col.key] ?? item?.[col.key]"
                      [type]="col.type"></flc-table-body-render>
                  </td>
                </ng-container>
                <ng-container *ngIf="">

                </ng-container>
              </ng-container>
            </ng-container>
          </tr>
        </ng-container>
        <ng-container *ngIf="queryType == 2">
          <tr>
            <td nzLeft>{{ i + 1}}</td>
            <td *ngFor="let col of tableHeader" [nzLeft]="col.disable || col.pinned">
              <flc-table-body-render [data]="data?.[col.key]" [type]="col.type"></flc-table-body-render>
            </td>
          </tr>
        </ng-container>
      </ng-container>
    </tbody>
  </nz-table>
</div>