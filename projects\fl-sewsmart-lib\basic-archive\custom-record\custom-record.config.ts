const factory_code = localStorage?.getItem('brand') || JSON.parse(localStorage?.getItem('userInfo') || '')?.factory_code;
const factoryCodes = ['CTC', 'CTC1', 'CTC22', 'CTCOTF250004'];

export const SearchOptions: Array<any> = [
  { type: 'local-select', label: '客户编码', options: [], key: 'code', relate_key: 'customer_code', placeHolder: '请选择' },
  { type: 'local-select', label: '客户简称', options: [], key: 'short_name', relate_key: 'short_name', placeHolder: '请选择' },
  {
    type: 'local-select',
    label: '收货国',
    key: 'receiver_country_name',
    relate_key: 'receiver_country_name',
    placeHolder: '请选择',
    options: [],
  },
  {
    type: 'local-select',
    label: '状态',
    key: 'status',
    relate_key: 'status',
    placeHolder: '请选择',
    options: [],
  },
  { type: 'local-select', label: '创建人', key: 'gen_user_name', relate_key: 'gen_user', options: [], placeHolder: '请选择' },
  {
    type: 'dateRange',
    label: '创建时间',
    key: 'gen_time',
    relate_key: 'gen_time',
    placeHolder: ['开始', '结束'],
  },
];

// 批量操作
export enum BatchActionEnum {
  enabled = 'enabled', // 批量启用
  disabled = 'disabled', // 批量禁用
  delete = 'delete', // 批量删除
}

export enum ButtonTypeEnum {
  delete = 'delete',
  back = 'back',
  edit = 'edit',
  cancel = 'cancel',
  save = 'save',
}

const commonTableHeaderConfig = {
  visible: true,
  pinned: false,
  disable: false,
  resizeble: true,
  sort: false,
  hasAction: true,
  detailBtn: true,
  sortOrderBy: null,
};

export const DefaultTableHeaders: any[] = [
  { label: '客户编码', key: 'code', width: '124px', type: 'text', ...commonTableHeaderConfig },
  { label: '客户全称', key: 'name', width: '184px', type: 'text', ...commonTableHeaderConfig },
  { label: '客户简称', key: 'short_name', width: '120px', type: 'text', ...commonTableHeaderConfig },
  // { label: '币种', key: 'unit_name', width: '80px', type: 'text', ...commonTableHeaderConfig },
  // { label: '额度', key: 'payment_quota', width: '80px', type: 'text', ...commonTableHeaderConfig },
  { label: '收货国', key: 'receiver_country_name', width: '168px', type: 'text', ...commonTableHeaderConfig },
  { label: '创建人', key: 'gen_user_name', width: '88px', type: 'text', ...commonTableHeaderConfig },
  { label: '创建时间', key: 'created_at', width: '148px', type: 'date', ...commonTableHeaderConfig },
];

// 详情页按钮集合
export const BtnConfig = [
  { type: 'default', label: 'back', text: '返回' },
  { type: 'default', label: 'cancel', text: '取消' },
  { type: 'pretty-minor', label: 'edit', text: '编辑' },
  { type: 'pretty-minor', label: 'save', text: '保存', icon: 'icon-baocun' },
];

// 操作模式
export enum FormModeEnum {
  ReadOnly,
  Create,
  Edit,
}

export const DetailFormList = [
  {
    type: 'input',
    label: '客户编码',
    code: 'code',
    columnkey: '',
    value: 'code',
    require: true,
    maxlength: factoryCodes.includes(factory_code) ? 100 : 20,
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '客户全称',
    code: 'name',
    columnkey: '',
    value: 'name',
    require: true,
    maxlength: factoryCodes.includes(factory_code) ? 100 : 20,
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '客户简称',
    code: 'short_name',
    columnkey: '',
    value: 'short_name',
    require: true,
    maxlength: factoryCodes.includes(factory_code) ? 100 : 20,
    nzSpan: 8,
  },
  {
    type: 'switch',
    label: '状态',
    code: 'status',
    columnkey: '',
    value: 'status',
    require: true,
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '统一社会信用代码',
    code: 'usci',
    columnkey: '',
    value: 'usci',
    require: false,
    maxlength: 100,
    nzSpan: 8,
  },
  // {
  //   type: 'local-select',
  //   label: '货币单位',
  //   code: 'unit_id',
  //   columnkey: 'unit',
  //   value: 'unit_name',
  //   require: false,
  //   options: [],
  //   nzSpan: 8,
  // },
  {
    type: 'number',
    label: '直接汇率',
    code: 'rate',
    tooltip: '直接汇率计算公式',
    columnkey: '',
    value: 'rate',
    require: false,
    min: 0,
    max: 999999999.9999,
    precision: 4,
    nzSpan: 8,
  },
  // {
  //   type: 'local-select',
  //   label: '币种',
  //   code: 'unit_id',
  //   columnkey: 'unit',
  //   value: 'unit_name',
  //   require: true,
  //   options: [],
  //   nzSpan: 8,
  // },
  // {
  //   type: 'number',
  //   label: '额度',
  //   code: 'payment_quota',
  //   columnkey: '',
  //   value: 'payment_quota',
  //   require: false,
  //   min: 0,
  //   max: 999999999.99,
  //   precision: 2,
  //   nzSpan: 8,
  // },
  {
    type: 'local-select',
    label: '运输方式',
    code: 'shipping_method',
    columnkey: 'shipping_method',
    value: 'shipping_method_name',
    require: false,
    options: [],
    nzSpan: 8,
  },
  {
    type: 'cascader',
    label: '收货国',
    code: 'receiver_country',
    columnkey: 'receiver_country_name',
    value: 'receiver_country_name',
    require: false,
    options: [],
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '收货港',
    code: 'receiver_port',
    columnkey: '',
    value: 'receiver_port',
    require: false,
    maxlength: 100,
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '详细地址',
    code: 'address',
    columnkey: '',
    require: false,
    value: 'address',
    maxlength: 255,
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '联系人',
    code: 'contact_name',
    value: 'contact_name',
    columnkey: '',
    require: false,
    maxlength: 50,
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '联系电话',
    code: 'contact_phone',
    value: 'contact_phone',
    columnkey: '',
    require: false,
    maxlength: 20,
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '开户行',
    code: 'deposit_bank',
    value: 'deposit_bank',
    columnkey: '',
    require: false,
    maxlength: 100,
    nzSpan: 8,
  },
  {
    type: 'input',
    label: '银行账号',
    code: 'bank_account',
    value: 'bank_account',
    columnkey: '',
    require: false,
    maxlength: 100,
    nzSpan: 8,
  },
  {
    type: 'dynamic-select',
    columnkey: 'payment_method',
    dataUrl: '/service/scm/dict_category/dict_option',
    label: '付款方式',
    code: 'payment_method_id',
    value: 'payment_method',
    require: false,
    nzSpan: 8,
  },

  {
    type: 'dynamic-select',
    columnkey: 'settlement_method',
    dataUrl: '/service/scm/dict_category/dict_option',
    label: '结算方式',
    code: 'settlement_method_id',
    value: 'settlement_method',
    require: false,
    nzSpan: 8,
  },
  {
    type: 'textarea',
    label: '备注',
    code: 'remark',
    value: 'remark',
    columnkey: '',
    require: false,
    maxlength: 255,
    nzSpan: 16,
  },
  {
    type: 'select',
    columnkey: 'name',
    dataUrl: '/service/archive/v1/api/user/basic_option',
    label: '系统账号',
    code: 'related_user_ids',
    value: 'related_user_names',
    require: false,
    nzSpan: 8,
  },
];
