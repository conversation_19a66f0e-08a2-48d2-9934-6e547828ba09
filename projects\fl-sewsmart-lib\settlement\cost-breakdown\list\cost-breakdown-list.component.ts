import { Component, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subject, finalize } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { FlcTableHelperService } from 'fl-common-lib';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { endOfDay, format, startOfDay } from 'date-fns';

import { CostBreakdownService } from '../cost-breakdown.service';
import { CostBreakdownItem, CostBreakdownListParams, CustomerOption } from '../models';
import { searchOptions, tableHeader } from './cost-breakdown-list.config';
import { MaterialInfoTabComponent } from '../components/material-info-tab/material-info-tab.component';

const version = '1.4.5';
@Component({
  selector: 'flss-cost-breakdown-list',
  templateUrl: './cost-breakdown-list.component.html',
  styleUrls: ['./cost-breakdown-list.component.scss'],
})
export class CostBreakdownListComponent implements OnInit, OnDestroy {
  @ViewChild(MaterialInfoTabComponent) materialInfoTab?: MaterialInfoTabComponent;

  private searchTimer: any = null;

  // 表单
  searchForm!: FormGroup;

  headers: any[] = tableHeader;
  costTableHeader: any[] = tableHeader;

  // 表格配置 - 适配 flc-table 组件
  tableConfig = {
    loading: false,
    dataList: [] as CostBreakdownItem[],
    count: 0,
    pageIndex: 1,
    pageSize: 20,
    tableName: 'costBreakdownList',
    hasCheckbox: false,
    hasAction: false,
    detailBtn: false,
    uniqueId: 'id',
    height: 300,
  };

  // 选中的行和详情显示
  selectedItem: CostBreakdownItem | null = null;
  showDetail = false;
  activeTabIndex = 0;

  // 表头字段设置
  btnHighLight = false;

  // 排序相关
  order_by: string[] = [];
  orderDateSortOrder: 'ascend' | 'descend' | null = null;

  // 物料筛选
  materialTypeFilter = {
    fabric: true,
    accessory: true,
  };

  // 物料类型选项配置
  materialTypeOptions = [
    { label: '面料', value: 'fabric', checked: true },
    { label: '辅料', value: 'accessory', checked: true },
  ];

  // 重置状态标记
  private isResetting = false;

  // 下拉选项
  customerOptions: CustomerOption[] = [];

  // 搜索配置
  searchOptions = searchOptions;

  // 日期快捷选择
  dateRanges = {
    本月: [new Date(new Date().getFullYear(), new Date().getMonth(), 1), new Date()],
    本季: this.getCurrentQuarter(),
    本年: [new Date(new Date().getFullYear(), 0, 1), new Date()],
  };

  // 详情标签页配置
  detailTabs = [
    { key: 'material', title: '物料信息', icon: 'inbox' },
    { key: 'processing', title: '加工信息', icon: 'tool' },
    { key: 'payment', title: '收款信息', icon: 'dollar' },
    { key: 'expense', title: '费用分摊', icon: 'pie-chart' },
  ];

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private message: NzMessageService,
    private costBreakdownService: CostBreakdownService,
    private _tableHelper: FlcTableHelperService,
    private _translate: TranslateService
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.initTableHeaders();
    this.loadCustomerOptions();
    this.getList();
  }

  ngOnDestroy() {
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
      this.searchTimer = null;
    }
  }

  private initForm() {
    this.searchForm = this.fb.group({
      order_date_range: [null],
      keyword: [''],
      order_code: [''],
      po_code: [''],
      style_code: [''],
      category: [''],
      customer: [''],
    });
  }

  /**
   * 初始化表头配置
   */
  private initTableHeaders() {
    // 从本地存储获取表头配置，如果没有则使用默认配置
    this.headers = this._tableHelper.getFlcTableHeaderConfig(tableHeader, version, this.tableConfig.tableName);
    this.getRenderHeaders();
  }

  private getCurrentQuarter(): [Date, Date] {
    const now = new Date();
    const quarter = Math.floor(now.getMonth() / 3);
    const startMonth = quarter * 3;
    return [new Date(now.getFullYear(), startMonth, 1), new Date(now.getFullYear(), startMonth + 3, 0)];
  }

  private loadCustomerOptions() {
    this.costBreakdownService.getListOptions().subscribe({
      next: (options: any) => {
        this.customerOptions = options.data.customer || [];
      },
      error: (error: any) => {
        // 加载客户选项失败
      },
    });
  }

  /**
   * 获取列表数据
   */
  getList(reset = false) {
    // 防止重复请求
    if (this.tableConfig.loading) {
      return;
    }

    if (reset) {
      this.tableConfig.pageIndex = 1;
    }

    this.tableConfig.loading = true;
    const formValue = this.searchForm.value;

    const params: CostBreakdownListParams = {
      page: this.tableConfig.pageIndex.toString(),
      page_size: this.tableConfig.pageSize.toString(),
      keyword: formValue.keyword || undefined,
      order_code: formValue.order_code || undefined,
      po_code: formValue.po_code || undefined,
      style_code: formValue.style_code || undefined,
      category: formValue.category || undefined,
      customer: formValue.customer || undefined,
      order_by: this.order_by,
    };

    // 处理日期范围
    if (formValue.order_date_range && formValue.order_date_range.length === 2) {
      params.order_date_start = format(startOfDay(formValue.order_date_range[0]), 'T');
      params.order_date_end = format(endOfDay(formValue.order_date_range[1]), 'T');
    }

    this.costBreakdownService.getList(params).subscribe({
      next: (response: any) => {
        const dataList = response.data.data || [];
        const count = parseInt(response.data.total) || 0;

        // this.tableConfig.loading = false;
        // this.tableConfig.dataList = dataList;
        // this.tableConfig.count = count;
        // this.tableConfig = { ...this.tableConfig };
        this.tableConfig = {
          ...this.tableConfig,
          dataList: dataList,
          count: count,
          loading: false,
        };
      },
      error: (error: any) => {
        this.message.error('获取数据失败');
        this.tableConfig.loading = false;
      },
    });
  }

  /**
   * 搜索 - 立即执行（用于按钮点击）
   */
  onSearch() {
    // 如果正在重置，跳过搜索
    if (this.isResetting) {
      return;
    }

    this.getList(true);
  }

  /**
   * 防抖搜索触发器 - 用于输入时触发
   */
  onInputSearch() {
    // 如果正在重置，跳过搜索
    if (this.isResetting) {
      return;
    }

    // 清除之前的定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer);
    }

    // 设置新的定时器，1秒后执行搜索
    this.searchTimer = setTimeout(() => {
      this.getList(true);
      this.searchTimer = null;
    }, 1000);
  }

  /**
   * 查看详情 - 在当前页面显示详情
   */
  onViewDetail(item: CostBreakdownItem) {
    this.selectedItem = item;
    this.showDetail = true;
    this.activeTabIndex = 0;
  }

  /**
   * 跳转到订单详情页面
   */
  onGoToOrderDetail(item: CostBreakdownItem, event: Event) {
    event.stopPropagation();

    if (!item.order_id) {
      this.message.warning('订单信息不完整，无法跳转');
      return;
    }

    // 根据订单ID或订单号跳转到订单详情页面
    // 这里使用订单ID作为路由参数，如果没有ID则使用订单号
    const routeParam = item.order_id;

    try {
      // 跳转到采购订单详情页面
      this.router.navigate(['/order/bulk/list', routeParam]);
    } catch (error) {
      this.message.error('跳转失败，请稍后重试');
    }
  }

  /**
   * 关闭详情面板
   */
  onCloseDetail() {
    this.showDetail = false;
    this.selectedItem = null;
  }

  /**
   * 详情标签页切换
   */
  onDetailTabChange(index: number) {
    this.activeTabIndex = index;
  }

  /**
   * flc-table 页码变化
   */
  onIndexChange(pageIndex: number) {
    this.tableConfig.pageIndex = pageIndex;
    this.getList();
  }

  /**
   * flc-table 页面大小变化
   */
  onSizeChange(pageSize: number) {
    this.tableConfig.pageSize = pageSize;
    this.tableConfig.pageIndex = 1;
    this.getList();
  }

  /**
   * flc-table 排序变化
   */
  onSortChange(sortData: any) {
    // 处理排序逻辑
    this.getList();
  }

  /**
   * 下单日期排序变化
   */
  onOrderDateSortChange(sortOrder: 'ascend' | 'descend' | null) {
    this.orderDateSortOrder = sortOrder;

    // 设置排序参数
    if (sortOrder === 'ascend') {
      this.order_by = ['order_date_asc'];
    } else if (sortOrder === 'descend') {
      this.order_by = ['order_date_desc'];
    } else {
      this.order_by = [];
    }

    // 重新获取数据
    this.getList(true);
  }

  /**
   * 重置搜索
   */
  onReset() {
    // 设置重置状态，防止表单值变化触发搜索
    this.isResetting = true;

    // 重置表单
    this.searchForm.reset();

    // 重新初始化表单默认值
    this.initForm();

    // 重置分页
    this.tableConfig.pageIndex = 1;

    // 延迟一下再重置状态，确保所有表单值变化事件都被跳过
    setTimeout(() => {
      this.isResetting = false;
      // 只调用一次数据加载
      this.getList(true);
    }, 100);
  }

  /**
   * 判断是否为特殊字段
   */
  isSpecialField(key: string): boolean {
    const specialFields = ['order_code', 'po_codes', 'po_due_dates', 'order_date', 'unit_prices', 'express_rates'];
    return specialFields.includes(key);
  }

  /**
   * 获取字段值
   */
  getFieldValue(data: any, key: string): string {
    const value = data[key];
    return value !== null && value !== undefined ? value : '-';
  }

  /**
   * 字段设置按钮点击
   */
  changeHeader(event: MouseEvent, _type: number): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));

    for (const item of shadow) {
      this._translate
        .get(item.label)
        .subscribe((res: string) => {
          item.label = res;
        })
        .unsubscribe();
    }
    this._tableHelper
      .openTableHeaderMidifyDialog<any>(shadow, event.target as HTMLElement, 'start')
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res: any) => {
        if (res) {
          for (const item of res) {
            item.label = this.headers.find((i) => i.key === item.key)?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
          // 保存表头配置到本地存储
          this._tableHelper.saveFlcTableHeaderConfig(this.headers, version, this.tableConfig.tableName);
        }
      });
  }

  getRenderHeaders() {
    this.costTableHeader = this._tableHelper.getRenderHeader<any>(this.headers);
  }

  /**
   * 物料筛选变化
   */
  onMaterialFilterChange(): void {
    // 更新materialTypeFilter对象
    this.materialTypeFilter.fabric = this.materialTypeOptions.some((option) => option.value === 'fabric' && option.checked);
    this.materialTypeFilter.accessory = this.materialTypeOptions.some((option) => option.value === 'accessory' && option.checked);

    // 触发物料信息组件重新获取数据
    this.refreshMaterialInfo();
  }

  /**
   * 刷新物料信息数据
   */
  private refreshMaterialInfo(): void {
    // 如果当前在物料信息tab且有选中的订单，则刷新数据
    if (this.activeTabIndex === 0 && this.selectedItem && this.materialInfoTab) {
      // 调用子组件的刷新方法
      this.materialInfoTab.refreshData();
    }
  }

  onResize({ width }: NzResizeEvent, col: string) {
    // this.headers = this._tableHelper.tableResize<any>(width, col, this.headers, version, 'headers');
    // this.getRenderHeaders();
    this.headers = this._tableHelper.tableResize<any>(width, col, this.headers, version, 'headers');
    this.getRenderHeaders();
  }
}
