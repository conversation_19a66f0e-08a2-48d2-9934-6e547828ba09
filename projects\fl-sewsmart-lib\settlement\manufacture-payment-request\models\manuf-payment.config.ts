import { PaymentType, PushKingdeeStatusEnum, SettleStatusEnum } from './manuf-payment-request.enum';
import { PaymentListItem } from './manuf-payment-request.interface';

/** 列表表头 */
export const paymentListTableHeader = [
  { label: '申请单号', key: 'code', type: 'template', templateName: 'code', disable: true },
  { label: '加工厂', key: 'factory_name', type: 'text' },
  { label: '发票税率(%)', key: 'tax_rate', type: 'text' },
  { label: '付款类型', key: 'type', type: 'template', templateName: 'type' },
  { label: '付款金额', key: 'price', type: 'quantity', precision: 5 },
  { label: '结算单号', key: 'process_settlement_codes', type: 'template', templateName: 'process_settlement_codes' },
  { label: '结算主体', key: 'party_archive_full_name', type: 'text' },
  { label: '付款日期', key: 'pay_time', type: 'date' },
  { label: '创建时间', key: 'created_at', type: 'datetime', width: '165px' },
  { label: '创建人', key: 'gen_user_name', type: 'text' },
  { label: '备注', key: 'remark', type: 'text' },
  { label: '状态', key: 'status', type: 'template', templateName: 'status', pinRight: true },
  {
    key: 'push_kingdee_status',
    label: '推送状态',
    width: '85px',
    type: 'text',
    style: getPushStatusColor,
    formatter: (item: PaymentListItem) => {
      if (item.status === SettleStatusEnum.toPay) {
        return item.push_kingdee_status === PushKingdeeStatusEnum.pushed ? '已推送' : '待推送';
      }
      return '';
    },
    pinRight: true,
  },
].map((item) => {
  return { pinned: false, visible: true, width: '120px', ...item };
});

/** 预付款表头配置 */
export const preTableHeader = [
  { label: '订单号', key: 'order_code', type: 'text', pinned: true },
  // { label: '图片', key: 'pictures', type: 'image' },
  { label: '工段', key: 'stages', type: 'template', templateName: 'stages' },
  { label: '品名', key: 'category', type: 'text' },
  { label: '款号', key: 'style_code', type: 'text' },
  { label: '接单日期', key: 'accept_time', type: 'date' },
  { label: '订单数', key: 'qty', type: 'quantity' },
  { label: '暂估金额', key: 'eval_price', type: 'quantity' },
  { label: '已付预付款', key: 'pre_paid_price', type: 'quantity' },
  { label: '已付货款', key: 'goods_paid_price', type: 'quantity' },
  { label: '已付金额', key: 'paid_price', type: 'quantity' },
  { label: '本次付款比', key: 'payment_ratio', type: 'quantity' },
  { label: '本次付款', key: 'process_payment_price', type: 'text', disable: true },
].map((item) => {
  return { pinned: false, visible: true, width: '120px', ...item };
});

/** 货款表头配置 */
export const goodsTableHeader = [
  { label: '结算单号', key: 'settlement_code', type: 'text', disable: true, pinned: true },
  { label: '结算主体', key: 'party_archive_full_name', type: 'text' },
  { label: '关联发票时间', key: 'invoice_time', type: 'date', width: '120px' },
  { label: '入库单号', key: 'inbound_code', type: 'text' },
  { label: '入库时间', key: 'inbound_date', type: 'date' },
  { label: '结算数', key: 'settlement_quantity', type: 'text' },
  { label: '货款金额', key: 'total_cost', type: 'quantity' },
  { label: '调整金额(计入)', key: 'include_adjustment_price', type: 'quantity', width: '120px' },
  { label: '调整金额(不计入)', key: 'exclude_adjustment_price', type: 'quantity', width: '130px' },
  { label: '结算金额', key: 'settlement_amount', type: 'quantity' },
].map((item) => {
  return { pinned: false, visible: true, width: '100px', ...item };
});

/**
 * 加工结算选择下拉抽屉
 */
export const manufSettleTableHeader = [
  { label: '结算单号', key: 'code', disable: true },
  { label: '加工厂', key: 'factory_name' },
  { label: '发票税率', key: 'tax_rate' },
  { label: '结算主体', key: 'party_archive_full_name' },
  { label: '入库总数量', key: 'inbound_quantity' },
  { label: '成本统计', key: 'total_cost' },
  { label: '结算总金额', key: 'settlement_price' },
  { label: '调整总金额', key: 'adjustment_price' },
  { label: '核销金额', key: 'used_price' },
  { label: '关联发票时间', key: 'invoice_time', type: 'date' },
  { label: '创建时间', key: 'created_at', type: 'datetime', sort: true },
  { label: '创建人', key: 'gen_user_name' },
].map((item) => {
  return { visible: true, type: 'text', width: '112px', sort: false, pinned: false, ...item };
});

/**
 * 成衣外发选择下拉抽屉
 */
export const outsourcingOrderTableHeader = [
  { label: '订单号', key: 'order_code', width: '230px' },
  { label: '大货单号', key: 'bulk_order', width: '230px' },
  { label: '加工厂', key: 'factory_name', templateName: 'factory_name', type: 'template', width: '146px' },
  { label: '工段', key: 'stages', templateName: 'stages', type: 'template', width: '146px' },
  { label: '总数量', key: 'qty', type: 'quantity', precision: 5, width: '146px' },
  { label: '暂估金额', key: 'total_price', type: 'quantity', precision: 5, width: '146px' },
  { label: '已付预付款(比例)', key: 'pre_paid_price', templateName: 'pre_paid_price', type: 'template', width: '146px' },
  { label: '接单时间', key: 'accept_time', type: 'datetime' },
  { label: '创建人', key: 'gen_user_name' },
].map((item) => {
  return { visible: true, type: 'text', width: '112px', sort: false, pinned: false, ...item };
});

export const PaymentTypeMap = {
  [PaymentType.prePaidPrice]: 'manufPaymentRequest.预付款',
  [PaymentType.goodsPrice]: 'manufPaymentRequest.货款',
};

export const StatusStyle = {
  [SettleStatusEnum.toSubmit]: {
    label: 'settlementCommon.待提交',
    color: '#138AFF',
    background: '#E7F3FE',
  },
  [SettleStatusEnum.toAudit]: {
    label: 'settlementCommon.待审核',
    color: '#FB6401',
    background: '#FFE8D9',
  },
  [SettleStatusEnum.toModify]: {
    label: 'settlementCommon.待修改',
    color: '#FF4A1D',
    background: '#FFE8E4',
  },
  [SettleStatusEnum.toPay]: {
    label: 'settlementCommon.待付款',
    color: '#FB6401',
    background: '#FFE8D9',
  },
  [SettleStatusEnum.paied]: {
    label: 'settlementCommon.已付款',
    color: '#54607C',
    background: '#E6E6E6',
  },
  [SettleStatusEnum.cancelled]: {
    label: 'settlementCommon.已取消',
    color: '#97999C',
    background: '#E6E6E6',
  },
};

function getPushStatusColor(item: PaymentListItem) {
  if (item.status !== SettleStatusEnum.toPay) return {};
  return item.push_kingdee_status === PushKingdeeStatusEnum.pushed ? { color: '#54607C' } : { color: '#138AFF' };
}
interface FieldItem {
  label: string;
  value: string;
  checked: boolean;
  formatter?: (item: any) => string;
}

// exportConfig需要传递给接口, value采用原始key
export const exportConfig: FieldItem[] = [
  { label: '申请单号', value: 'code', checked: true },
  { label: '加工厂', value: 'factory_name', checked: true },
  { label: '发票税率(%)', value: 'invoice_tax_rate', checked: true },
  { label: '付款类型', value: 'payment_type', checked: true },
  { label: '付款金额', value: 'price', checked: true },
  { label: '付款日期', value: 'payment_time', checked: true },
  { label: '创建时间', value: 'gen_time', checked: true },
  { label: '创建人', value: 'gen_user_name', checked: true },
  { label: '备注', value: 'remark', checked: true },
  { label: '状态', value: 'status_label', checked: true },
  { label: '推送状态', value: 'push_kingdee_status', checked: true },
  { label: '结算单号', value: 'process_settlement_code', checked: true },
  { label: '结算主体', value: 'party_archive_full_name', checked: true },
  { label: '关联发票时间', value: 'invoice_time', checked: true },
  { label: '入库单号', value: 'inbound_code', checked: true },
  { label: '入库类型', value: 'inbound_type', checked: true },
  { label: '入库时间', value: 'inbound_time', checked: true },
  { label: '款式编码', value: 'style_code', checked: true },
  { label: '大货单号', value: 'order_code', checked: true },
  { label: '入库数量', value: 'inbound_qty', checked: true },
  { label: '实际单价(均价)', value: 'eval_price', checked: true },
  { label: '成本小计', value: 'total_cast', checked: true },
  { label: '调整金额(计入', value: 'count_adjustment_price', checked: true },
  { label: '调整金额(不计入)', value: 'not_count_adjustment_price', checked: true },
  { label: '结算金额', value: 'settlement_price', checked: true },
  { label: '预付款', value: 'pre_paid_price', checked: true },
  { label: '剩余应付', value: 'not_pay_amount', checked: true },
  { label: '本次付款', value: 'process_payment_price', checked: true },
  { label: '单价', value: 'unit_price', checked: true },
  { label: '税率', value: 'rate', checked: true },
  { label: '含税单价', value: 'tax_price', checked: true },
];
